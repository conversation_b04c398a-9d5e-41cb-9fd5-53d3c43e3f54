import 'package:flutter/material.dart';
import '../utils/constants.dart';

class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? watch;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.watch,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < BreakPoints.mobile) {
          return watch ?? mobile;
        } else if (constraints.maxWidth < BreakPoints.tablet) {
          return mobile;
        } else if (constraints.maxWidth < BreakPoints.desktop) {
          return tablet ?? mobile;
        } else {
          return desktop ?? tablet ?? mobile;
        }
      },
    );
  }
}

class BreakPoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
  static const double watch = 300;
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final double childAspectRatio;
  final EdgeInsetsGeometry? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = AppConstants.paddingMedium,
    this.runSpacing = AppConstants.paddingMedium,
    this.childAspectRatio = 1.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        int crossAxisCount = _getCrossAxisCount(constraints.maxWidth);

        return Padding(
          padding: padding ?? const EdgeInsets.all(AppConstants.paddingMedium),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: spacing,
              mainAxisSpacing: runSpacing,
              childAspectRatio: childAspectRatio,
            ),
            itemCount: children.length,
            itemBuilder: (context, index) => children[index],
          ),
        );
      },
    );
  }

  int _getCrossAxisCount(double width) {
    if (width < BreakPoints.mobile) {
      return 1;
    } else if (width < BreakPoints.tablet) {
      return 2;
    } else if (width < BreakPoints.desktop) {
      return 3;
    } else {
      return 4;
    }
  }
}

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double containerWidth = maxWidth ?? _getMaxWidth(constraints.maxWidth);

        return Center(
          child: Container(
            width: constraints.maxWidth > containerWidth
                ? containerWidth
                : constraints.maxWidth,
            padding: padding ?? _getResponsivePadding(constraints.maxWidth),
            margin: margin,
            child: child,
          ),
        );
      },
    );
  }

  double _getMaxWidth(double screenWidth) {
    if (screenWidth < BreakPoints.tablet) {
      return screenWidth;
    } else if (screenWidth < BreakPoints.desktop) {
      return BreakPoints.tablet;
    } else {
      return BreakPoints.desktop;
    }
  }

  EdgeInsetsGeometry _getResponsivePadding(double width) {
    if (width < BreakPoints.mobile) {
      return const EdgeInsets.all(AppConstants.paddingSmall);
    } else if (width < BreakPoints.tablet) {
      return const EdgeInsets.all(AppConstants.paddingMedium);
    } else {
      return const EdgeInsets.all(AppConstants.paddingLarge);
    }
  }
}

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText({
    super.key,
    required this.text,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double fontSize = _getResponsiveFontSize(constraints.maxWidth);

        return Text(
          text,
          style: (style ?? const TextStyle()).copyWith(fontSize: fontSize),
          textAlign: textAlign,
          maxLines: maxLines,
          overflow: overflow,
        );
      },
    );
  }

  double _getResponsiveFontSize(double width) {
    double baseFontSize = style?.fontSize ?? AppConstants.fontSizeMedium;

    if (width < BreakPoints.mobile) {
      return baseFontSize * 0.9;
    } else if (width < BreakPoints.tablet) {
      return baseFontSize;
    } else {
      return baseFontSize * 1.1;
    }
  }
}

class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool forceColumn;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.forceColumn = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        bool shouldUseColumn =
            forceColumn || constraints.maxWidth < BreakPoints.mobile;

        if (shouldUseColumn) {
          return Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children,
          );
        } else {
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children,
          );
        }
      },
    );
  }
}

class ResponsiveWrap extends StatelessWidget {
  final List<Widget> children;
  final WrapAlignment alignment;
  final WrapCrossAlignment crossAxisAlignment;
  final double spacing;
  final double runSpacing;

  const ResponsiveWrap({
    super.key,
    required this.children,
    this.alignment = WrapAlignment.start,
    this.crossAxisAlignment = WrapCrossAlignment.start,
    this.spacing = AppConstants.paddingSmall,
    this.runSpacing = AppConstants.paddingSmall,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double responsiveSpacing = _getResponsiveSpacing(constraints.maxWidth);

        return Wrap(
          alignment: alignment,
          crossAxisAlignment: crossAxisAlignment,
          spacing: responsiveSpacing,
          runSpacing: responsiveSpacing,
          children: children,
        );
      },
    );
  }

  double _getResponsiveSpacing(double width) {
    if (width < BreakPoints.mobile) {
      return spacing * 0.8;
    } else if (width < BreakPoints.tablet) {
      return spacing;
    } else {
      return spacing * 1.2;
    }
  }
}

// مساعد للحصول على معلومات الشاشة
class ScreenInfo {
  final BuildContext context;

  ScreenInfo(this.context);

  Size get size => MediaQuery.of(context).size;
  double get width => size.width;
  double get height => size.height;
  double get aspectRatio => width / height;
  Orientation get orientation => MediaQuery.of(context).orientation;

  bool get isPortrait => orientation == Orientation.portrait;
  bool get isLandscape => orientation == Orientation.landscape;

  bool get isMobile => width < BreakPoints.mobile;
  bool get isTablet =>
      width >= BreakPoints.mobile && width < BreakPoints.desktop;
  bool get isDesktop => width >= BreakPoints.desktop;
  bool get isWatch => width < BreakPoints.watch;

  DeviceType get deviceType {
    if (isWatch) return DeviceType.watch;
    if (isMobile) return DeviceType.mobile;
    if (isTablet) return DeviceType.tablet;
    return DeviceType.desktop;
  }

  double get statusBarHeight => MediaQuery.of(context).padding.top;
  double get bottomPadding => MediaQuery.of(context).padding.bottom;
  double get keyboardHeight => MediaQuery.of(context).viewInsets.bottom;

  bool get hasNotch => statusBarHeight > 24;
  bool get isKeyboardOpen => keyboardHeight > 0;

  EdgeInsets get safeArea => MediaQuery.of(context).padding;
  EdgeInsets get viewInsets => MediaQuery.of(context).viewInsets;

  double get textScaleFactor => MediaQuery.of(context).textScaler.scale(1.0);
  Brightness get brightness => MediaQuery.of(context).platformBrightness;

  bool get isDarkMode => brightness == Brightness.dark;
  bool get isLightMode => brightness == Brightness.light;
}

enum DeviceType { watch, mobile, tablet, desktop }

// Extension للحصول على معلومات الشاشة بسهولة
extension ContextExtensions on BuildContext {
  ScreenInfo get screen => ScreenInfo(this);

  bool get isMobile => screen.isMobile;
  bool get isTablet => screen.isTablet;
  bool get isDesktop => screen.isDesktop;
  bool get isWatch => screen.isWatch;

  double get screenWidth => screen.width;
  double get screenHeight => screen.height;

  bool get isPortrait => screen.isPortrait;
  bool get isLandscape => screen.isLandscape;

  bool get isDarkMode => screen.isDarkMode;
  bool get isLightMode => screen.isLightMode;
}

// مكون للتحكم في التخطيط حسب الاتجاه
class OrientationLayout extends StatelessWidget {
  final Widget portrait;
  final Widget? landscape;

  const OrientationLayout({super.key, required this.portrait, this.landscape});

  @override
  Widget build(BuildContext context) {
    return OrientationBuilder(
      builder: (context, orientation) {
        if (orientation == Orientation.landscape) {
          return landscape ?? portrait;
        }
        return portrait;
      },
    );
  }
}

// مكون للتحكم في المحتوى حسب حجم الشاشة
class AdaptiveWidget extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;

  const AdaptiveWidget({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        DeviceType deviceType = context.screen.deviceType;
        return builder(context, deviceType);
      },
    );
  }
}

// مكون لعرض قائمة بتأثيرات متحركة متدرجة
class StaggeredAnimationList extends StatefulWidget {
  final List<Widget> children;
  final Duration delay;
  final Duration duration;
  final Curve curve;

  const StaggeredAnimationList({
    super.key,
    required this.children,
    this.delay = const Duration(milliseconds: 100),
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeOut,
  });

  @override
  State<StaggeredAnimationList> createState() => _StaggeredAnimationListState();
}

class _StaggeredAnimationListState extends State<StaggeredAnimationList>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(duration: widget.duration, vsync: this),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: widget.curve));
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(widget.delay * i, () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(widget.children.length, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _animations[index].value)),
              child: Opacity(
                opacity: _animations[index].value,
                child: widget.children[index],
              ),
            );
          },
        );
      }),
    );
  }
}
