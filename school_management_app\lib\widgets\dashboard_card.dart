import 'package:flutter/material.dart';
import '../utils/constants.dart';

class DashboardCard extends StatefulWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final String? subtitle;
  final Widget? trailing;
  final bool showProgress;
  final double? progressValue;

  const DashboardCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    this.onTap,
    this.subtitle,
    this.trailing,
    this.showProgress = false,
    this.progressValue,
  });

  @override
  State<DashboardCard> createState() => _DashboardCardState();
}

class _DashboardCardState extends State<DashboardCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FadeTransition(opacity: _fadeAnimation, child: _buildCard()),
        );
      },
    );
  }

  Widget _buildCard() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        gradient: LinearGradient(
          colors: [
            widget.color.withValues(alpha: 0.1),
            widget.color.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: widget.color.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          splashColor: widget.color.withValues(alpha: 0.1),
          highlightColor: widget.color.withValues(alpha: 0.05),
          child: Container(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: widget.color.withValues(alpha: 0.15),
                        borderRadius: BorderRadius.circular(
                          AppConstants.radiusMedium,
                        ),
                      ),
                      child: Icon(
                        widget.icon,
                        size: AppConstants.iconSizeLarge,
                        color: widget.color,
                      ),
                    ),
                    if (widget.trailing != null) widget.trailing!,
                  ],
                ),
                const SizedBox(height: AppConstants.paddingLarge),
                Text(
                  widget.value,
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeHeading,
                    fontWeight: AppConstants.fontWeightBold,
                    color: widget.color,
                    height: 1.0,
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),
                Text(
                  widget.title,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeLarge,
                    fontWeight: AppConstants.fontWeightMedium,
                    color: AppConstants.textPrimaryColor,
                  ),
                ),
                if (widget.subtitle != null) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    widget.subtitle!,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: AppConstants.fontWeightRegular,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                ],
                if (widget.showProgress && widget.progressValue != null) ...[
                  const SizedBox(height: AppConstants.paddingMedium),
                  _buildProgressBar(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'التقدم',
              style: TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: AppConstants.fontWeightMedium,
                color: AppConstants.textSecondaryColor,
              ),
            ),
            Text(
              '${(widget.progressValue! * 100).toInt()}%',
              style: TextStyle(
                fontSize: AppConstants.fontSizeSmall,
                fontWeight: AppConstants.fontWeightBold,
                color: widget.color,
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.paddingSmall),
        TweenAnimationBuilder<double>(
          duration: AppConstants.animationDurationLong,
          tween: Tween(begin: 0, end: widget.progressValue!),
          builder: (context, animatedValue, child) {
            return LinearProgressIndicator(
              value: animatedValue,
              backgroundColor: widget.color.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation(widget.color),
              minHeight: 6,
              borderRadius: BorderRadius.circular(3),
            );
          },
        ),
      ],
    );
  }
}
