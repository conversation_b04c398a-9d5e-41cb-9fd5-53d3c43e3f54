import 'package:flutter/material.dart';

class AppConstants {
  // App Info
  static const String appName = 'School Management';
  static const String appVersion = '1.0.0';

  // Modern Color Palette
  static const Color primaryColor = Color(0xFF1E3A8A); // Deep Blue
  static const Color primaryLight = Color(0xFF3B82F6); // Bright Blue
  static const Color primaryDark = Color(0xFF1E40AF); // Darker Blue
  static const Color secondary = Color(0xFFF59E0B); // Amber/Gold
  static const Color secondaryLight = Color(0xFFFBBF24); // Light Amber
  static const Color accent = Color(0xFF8B5CF6); // Purple

  // Background Colors
  static const Color backgroundColor = Color(0xFFF8FAFC); // Very Light Gray
  static const Color surfaceColor = Color(0xFFFFFFFF); // Pure White
  static const Color cardBackground = Color(0xFFF1F5F9); // Light Gray for cards
  static const Color overlayColor = Color(0x80000000); // Semi-transparent black

  // Status Colors
  static const Color successColor = Color(0xFF10B981); // Emerald Green
  static const Color successLight = Color(0xFF34D399); // Light Green
  static const Color warningColor = Color(0xFFF59E0B); // Amber
  static const Color warningLight = Color(0xFFFBBF24); // Light Amber
  static const Color errorColor = Color(0xFFEF4444); // Red
  static const Color errorLight = Color(0xFFF87171); // Light Red
  static const Color infoColor = Color(0xFF3B82F6); // Blue
  static const Color infoLight = Color(0xFF60A5FA); // Light Blue

  // Text Colors
  static const Color textPrimaryColor = Color(0xFF1F2937); // Dark Gray
  static const Color textSecondaryColor = Color(0xFF6B7280); // Medium Gray
  static const Color textTertiaryColor = Color(0xFF9CA3AF); // Light Gray
  static const Color textOnPrimaryColor = Color(0xFFFFFFFF); // White
  static const Color textOnDarkColor = Color(0xFFFFFFFF); // White

  // Border Colors
  static const Color borderColor = Color(0xFFE5E7EB); // Light Gray
  static const Color borderFocusColor = Color(0xFF3B82F6); // Blue
  static const Color dividerColor = Color(0xFFE5E7EB); // Light Gray

  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [successColor, successLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient = LinearGradient(
    colors: [surfaceColor, cardBackground],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;

  // Typography Scale
  static const double fontSizeCaption = 10.0; // Very small text
  static const double fontSizeSmall = 12.0; // Small text
  static const double fontSizeMedium = 14.0; // Body text
  static const double fontSizeLarge = 16.0; // Large body text
  static const double fontSizeXLarge = 18.0; // Subtitle
  static const double fontSizeXXLarge = 20.0; // Small heading
  static const double fontSizeTitle = 24.0; // Title
  static const double fontSizeHeading = 28.0; // Large heading
  static const double fontSizeDisplay = 32.0; // Display text

  // Font Weights
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;
  static const FontWeight fontWeightExtraBold = FontWeight.w800;

  // Animation Durations
  static const Duration animationDurationFast = Duration(milliseconds: 150);
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);
  static const Duration animationDurationSlow = Duration(milliseconds: 800);

  // Elevation Levels
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  static const double elevationVeryHigh = 16.0;

  // Icon Sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;
  static const double iconSizeXXLarge = 64.0;

  // Button Heights
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;
  static const double buttonHeightXLarge = 56.0;

  // User Roles
  static const Map<String, String> userRoleNames = {
    'admin': 'مدير',
    'teacher': 'معلم',
    'student': 'طالب',
    'parent': 'ولي أمر',
  };

  // Subjects
  static const List<String> subjects = [
    'الرياضيات',
    'الفيزياء',
    'الكيمياء',
    'الأحياء',
    'اللغة العربية',
    'اللغة الإنجليزية',
    'التاريخ',
    'الجغرافيا',
    'التربية الإسلامية',
    'الحاسوب',
    'الفنون',
    'التربية البدنية',
  ];

  // Classes
  static const List<String> classes = [
    'الصف الأول',
    'الصف الثاني',
    'الصف الثالث',
    'الصف الرابع',
    'الصف الخامس',
    'الصف السادس',
    'الصف السابع',
    'الصف الثامن',
    'الصف التاسع',
    'الصف العاشر',
    'الصف الحادي عشر',
    'الصف الثاني عشر',
  ];

  // Time Slots
  static const List<String> timeSlots = [
    '07:30 - 08:15',
    '08:15 - 09:00',
    '09:00 - 09:45',
    '09:45 - 10:30',
    '10:45 - 11:30',
    '11:30 - 12:15',
    '12:15 - 13:00',
    '13:00 - 13:45',
  ];

  // Days of Week
  static const List<String> daysOfWeek = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
  ];

  // File Extensions
  static const List<String> allowedFileExtensions = [
    'pdf',
    'doc',
    'docx',
    'jpg',
    'jpeg',
    'png',
  ];

  // Max File Size (in bytes)
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
}

// Responsive Breakpoints
class BreakPoints {
  static const double watch = 300;
  static const double mobile = 480;
  static const double tablet = 768;
  static const double desktop = 1024;
  static const double largeDesktop = 1440;
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.light,
        primary: AppConstants.primaryColor,
        secondary: AppConstants.secondary,
        surface: AppConstants.surfaceColor,

        error: AppConstants.errorColor,
      ),
      fontFamily: 'Cairo',

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: AppConstants.textOnPrimaryColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: AppConstants.fontWeightSemiBold,
          color: AppConstants.textOnPrimaryColor,
          fontFamily: 'Cairo',
        ),
        iconTheme: const IconThemeData(
          color: AppConstants.textOnPrimaryColor,
          size: AppConstants.iconSizeMedium,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: AppConstants.elevationLow,
        shadowColor: AppConstants.primaryColor.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
        color: AppConstants.surfaceColor,
        margin: const EdgeInsets.all(AppConstants.paddingSmall),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: AppConstants.textOnPrimaryColor,
          elevation: AppConstants.elevationMedium,
          shadowColor: AppConstants.primaryColor.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingXLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: AppConstants.fontWeightSemiBold,
            fontFamily: 'Cairo',
          ),
          minimumSize: const Size(0, AppConstants.buttonHeightLarge),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppConstants.primaryColor,
          side: const BorderSide(color: AppConstants.primaryColor, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingXLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: AppConstants.fontWeightSemiBold,
            fontFamily: 'Cairo',
          ),
          minimumSize: const Size(0, AppConstants.buttonHeightLarge),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppConstants.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: AppConstants.fontWeightSemiBold,
            fontFamily: 'Cairo',
          ),
        ),
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppConstants.surfaceColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: const BorderSide(
            color: AppConstants.borderColor,
            width: 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: const BorderSide(
            color: AppConstants.borderColor,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: const BorderSide(
            color: AppConstants.borderFocusColor,
            width: 2.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: const BorderSide(
            color: AppConstants.errorColor,
            width: 1.0,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingLarge,
          vertical: AppConstants.paddingMedium,
        ),
        labelStyle: const TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          color: AppConstants.textSecondaryColor,
          fontFamily: 'Cairo',
        ),
        hintStyle: const TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          color: AppConstants.textTertiaryColor,
          fontFamily: 'Cairo',
        ),
      ),

      // Text Themes
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: AppConstants.fontSizeDisplay,
          fontWeight: AppConstants.fontWeightBold,
          color: AppConstants.textPrimaryColor,
          fontFamily: 'Cairo',
        ),
        headlineLarge: TextStyle(
          fontSize: AppConstants.fontSizeHeading,
          fontWeight: AppConstants.fontWeightBold,
          color: AppConstants.textPrimaryColor,
          fontFamily: 'Cairo',
        ),
        titleLarge: TextStyle(
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: AppConstants.fontWeightSemiBold,
          color: AppConstants.textPrimaryColor,
          fontFamily: 'Cairo',
        ),
        bodyLarge: TextStyle(
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: AppConstants.fontWeightRegular,
          color: AppConstants.textPrimaryColor,
          fontFamily: 'Cairo',
        ),
        bodyMedium: TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: AppConstants.fontWeightRegular,
          color: AppConstants.textPrimaryColor,
          fontFamily: 'Cairo',
        ),
        labelLarge: TextStyle(
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: AppConstants.fontWeightMedium,
          color: AppConstants.textSecondaryColor,
          fontFamily: 'Cairo',
        ),
      ),

      // Icon Theme
      iconTheme: const IconThemeData(
        color: AppConstants.textSecondaryColor,
        size: AppConstants.iconSizeMedium,
      ),

      // Divider Theme
      dividerTheme: const DividerThemeData(
        color: AppConstants.dividerColor,
        thickness: 1.0,
        space: 1.0,
      ),

      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppConstants.surfaceColor,
        selectedItemColor: AppConstants.primaryColor,
        unselectedItemColor: AppConstants.textSecondaryColor,
        type: BottomNavigationBarType.fixed,
        elevation: AppConstants.elevationHigh,
        selectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: AppConstants.fontWeightSemiBold,
          fontFamily: 'Cairo',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: AppConstants.fontWeightRegular,
          fontFamily: 'Cairo',
        ),
      ),

      // Tab Bar Theme
      tabBarTheme: const TabBarThemeData(
        labelColor: AppConstants.primaryColor,
        unselectedLabelColor: AppConstants.textSecondaryColor,
        indicatorColor: AppConstants.primaryColor,
        labelStyle: TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: AppConstants.fontWeightSemiBold,
          fontFamily: 'Cairo',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: AppConstants.fontWeightRegular,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }

  // Dark Theme (للمستقبل)
  static ThemeData get darkTheme {
    return lightTheme.copyWith(
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.dark,
      ),
    );
  }
}
