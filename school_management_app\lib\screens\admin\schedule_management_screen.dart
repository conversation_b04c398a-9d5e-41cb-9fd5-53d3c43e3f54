import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../../utils/constants.dart';
import '../../widgets/file_upload_card.dart';

class ScheduleManagementScreen extends StatefulWidget {
  const ScheduleManagementScreen({super.key});

  @override
  State<ScheduleManagementScreen> createState() =>
      _ScheduleManagementScreenState();
}

class _ScheduleManagementScreenState extends State<ScheduleManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // General Schedule
  String? _generalSchedulePath;
  bool _isUploadingGeneral = false;
  String? _selectedDepartment; // متوسط أو ثانوي
  bool _isConfirmingGeneralUpload = false;

  // Teacher Schedules
  String? _selectedTeacherDepartment;
  String? _selectedTeacher;
  String? _teacherSchedulePath;
  bool _isUploadingTeacher = false;
  bool _isConfirmingTeacherUpload = false;

  // قائمة المعلمين مع أقسامهم
  final Map<String, List<String>> _teachersByDepartment = {
    'متوسط': [
      'فاطمة علي - رياضيات',
      'محمد سالم - لغة عربية',
      'نورة محمد - علوم',
    ],
    'ثانوي': [
      'أحمد محمود - فيزياء',
      'سارة أحمد - كيمياء',
      'خالد عبدالله - أحياء',
    ],
    'كلاهما': ['عبدالرحمن علي - رياضيات', 'منى سعيد - لغة إنجليزية'],
  };

  // الحصول على قائمة المعلمين حسب القسم المختار
  List<String> _getTeachersByDepartment() {
    if (_selectedTeacherDepartment == null) {
      return [];
    }

    List<String> teachers = [];

    // إضافة المعلمين من القسم المختار
    if (_teachersByDepartment.containsKey(_selectedTeacherDepartment)) {
      teachers.addAll(_teachersByDepartment[_selectedTeacherDepartment]!);
    }

    // إضافة المعلمين المسجلين في القسمين
    if (_selectedTeacherDepartment != 'كلاهما') {
      teachers.addAll(_teachersByDepartment['كلاهما']!);
    }

    return teachers;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _pickGeneralSchedule() async {
    if (_selectedDepartment == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار القسم أولاً (متوسط أو ثانوي)'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    try {
      setState(() => _isUploadingGeneral = true);

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _generalSchedulePath = result.files.first.path;
          _isConfirmingGeneralUpload = false; // Reset confirmation state
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الملف: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isUploadingGeneral = false);
    }
  }

  Future<void> _confirmGeneralScheduleUpload() async {
    if (_generalSchedulePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار ملف أولاً'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    try {
      setState(() => _isConfirmingGeneralUpload = true);

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم رفع الجدول العام لقسم $_selectedDepartment بنجاح',
            ),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفع الملف: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isConfirmingGeneralUpload = false);
    }
  }

  Future<void> _pickTeacherSchedule() async {
    if (_selectedTeacherDepartment == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار القسم أولاً'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    if (_selectedTeacher == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار المعلم أولاً'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    try {
      setState(() => _isUploadingTeacher = true);

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _teacherSchedulePath = result.files.first.path;
          _isConfirmingTeacherUpload = false; // Reset confirmation state
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الملف: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isUploadingTeacher = false);
    }
  }

  Future<void> _confirmTeacherScheduleUpload() async {
    if (_teacherSchedulePath == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار ملف أولاً'),
          backgroundColor: AppConstants.warningColor,
        ),
      );
      return;
    }

    try {
      setState(() => _isConfirmingTeacherUpload = true);

      // Simulate upload delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم رفع جدول $_selectedTeacher بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفع الملف: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isConfirmingTeacherUpload = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TabBar(
          controller: _tabController,
          labelColor: AppConstants.primaryColor,
          unselectedLabelColor: AppConstants.textSecondaryColor,
          indicatorColor: AppConstants.primaryColor,
          tabs: const [
            Tab(text: 'الجدول العام'),
            Tab(text: 'جداول المعلمين'),
          ],
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildGeneralScheduleTab(), _buildTeacherScheduleTab()],
          ),
        ),
      ],
    );
  }

  Widget _buildGeneralScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'رفع الجدول العام',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'يمكن لجميع المستخدمين الوصول إلى الجدول العام',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingLarge),

          // Department Selection
          DropdownButtonFormField<String>(
            value: _selectedDepartment,
            decoration: const InputDecoration(
              labelText: 'اختيار القسم',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.school),
            ),
            items: const [
              DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
              DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedDepartment = value;
                // Reset file path when department changes
                _generalSchedulePath = null;
              });
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          FileUploadCard(
            title: _selectedDepartment != null
                ? 'الجدول العام لقسم $_selectedDepartment'
                : 'الجدول العام للمدرسة',
            subtitle: 'ملف PDF - حد أقصى 10 ميجابايت',
            icon: Icons.calendar_today,
            filePath: _generalSchedulePath,
            isUploading: _isUploadingGeneral,
            onUpload: _pickGeneralSchedule,
            onRemove: () {
              setState(() {
                _generalSchedulePath = null;
              });
            },
          ),

          if (_generalSchedulePath != null) ...[
            const SizedBox(height: AppConstants.paddingMedium),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isConfirmingGeneralUpload
                    ? null
                    : _confirmGeneralScheduleUpload,
                icon: _isConfirmingGeneralUpload
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppConstants.textOnPrimaryColor,
                          ),
                        ),
                      )
                    : const Icon(Icons.cloud_upload),
                label: Text(
                  _isConfirmingGeneralUpload
                      ? 'جاري الرفع...'
                      : 'تأكيد رفع الملف',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.successColor,
                  padding: const EdgeInsets.symmetric(
                    vertical: AppConstants.paddingMedium,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTeacherScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'رفع جداول المعلمين',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          const Text(
            'جداول أسبوعية خاصة بكل معلم',
            style: TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          const SizedBox(height: AppConstants.paddingLarge),

          // Department Selection
          DropdownButtonFormField<String>(
            value: _selectedTeacherDepartment,
            decoration: const InputDecoration(
              labelText: 'اختيار القسم',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.school),
            ),
            items: const [
              DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
              DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
              DropdownMenuItem(value: 'كلاهما', child: Text('كلاهما')),
            ],
            onChanged: (value) {
              setState(() {
                _selectedTeacherDepartment = value;
                _selectedTeacher = null; // Reset teacher selection
                _teacherSchedulePath = null; // Reset file path
              });
            },
          ),

          const SizedBox(height: AppConstants.paddingMedium),

          // Teacher Selection
          if (_selectedTeacherDepartment != null)
            DropdownButtonFormField<String>(
              value: _selectedTeacher,
              decoration: const InputDecoration(
                labelText: 'اختيار المعلم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              items: _getTeachersByDepartment().map((teacher) {
                return DropdownMenuItem(value: teacher, child: Text(teacher));
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedTeacher = value;
                  _teacherSchedulePath = null; // Reset file path
                });
              },
            ),

          const SizedBox(height: AppConstants.paddingLarge),

          if (_selectedTeacher != null) ...[
            FileUploadCard(
              title: 'جدول $_selectedTeacher',
              subtitle: 'ملف PDF - حد أقصى 10 ميجابايت',
              icon: Icons.person,
              filePath: _teacherSchedulePath,
              isUploading: _isUploadingTeacher,
              onUpload: _pickTeacherSchedule,
              onRemove: () {
                setState(() {
                  _teacherSchedulePath = null;
                });
              },
            ),

            if (_teacherSchedulePath != null) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isConfirmingTeacherUpload
                      ? null
                      : _confirmTeacherScheduleUpload,
                  icon: _isConfirmingTeacherUpload
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppConstants.textOnPrimaryColor,
                            ),
                          ),
                        )
                      : const Icon(Icons.cloud_upload),
                  label: Text(
                    _isConfirmingTeacherUpload
                        ? 'جاري الرفع...'
                        : 'تأكيد رفع الملف',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.successColor,
                    padding: const EdgeInsets.symmetric(
                      vertical: AppConstants.paddingMedium,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ],
      ),
    );
  }
}
