import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../utils/constants.dart';
import '../../models/user.dart';
import '../../services/user_service.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final UserService _userService = UserService();

  bool _isLoading = false;
  bool _isExporting = false;

  // تاريخ البداية والنهاية للتقارير
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();

  // بيانات المعلمين
  final List<Map<String, dynamic>> _teachers = [
    {
      'id': '1',
      'name': 'فاطمة علي',
      'department': 'متوسط',
      'subjects': ['الرياضيات', 'العلوم'],
      'email': '<EMAIL>',
      'phone': '**********',
      'lessonsCompleted': 45,
      'lessonsTotal': 50,
      'missingDates': ['2023-12-15', '2023-12-20'],
      'lastActivity': '2023-12-25',
    },
    {
      'id': '2',
      'name': 'محمد سالم',
      'department': 'ثانوي',
      'subjects': ['اللغة العربية'],
      'email': '<EMAIL>',
      'phone': '**********',
      'lessonsCompleted': 48,
      'lessonsTotal': 50,
      'missingDates': ['2023-12-18'],
      'lastActivity': '2023-12-26',
    },
    {
      'id': '3',
      'name': 'أحمد محمود',
      'department': 'ثانوي',
      'subjects': ['الفيزياء', 'الكيمياء'],
      'email': '<EMAIL>',
      'phone': '0555345678',
      'lessonsCompleted': 50,
      'lessonsTotal': 50,
      'missingDates': [],
      'lastActivity': '2023-12-27',
    },
    {
      'id': '4',
      'name': 'نورة محمد',
      'department': 'متوسط',
      'subjects': ['اللغة الإنجليزية'],
      'email': '<EMAIL>',
      'phone': '0555901234',
      'lessonsCompleted': 42,
      'lessonsTotal': 50,
      'missingDates': ['2023-12-10', '2023-12-17', '2023-12-24'],
      'lastActivity': '2023-12-25',
    },
    {
      'id': '5',
      'name': 'عبدالرحمن علي',
      'department': 'كلاهما',
      'subjects': ['الرياضيات'],
      'email': '<EMAIL>',
      'phone': '0555567890',
      'lessonsCompleted': 47,
      'lessonsTotal': 50,
      'missingDates': ['2023-12-22', '2023-12-23'],
      'lastActivity': '2023-12-26',
    },
  ];

  // بيانات الطلاب
  final List<Map<String, dynamic>> _students = [
    {
      'id': 'M1A1001',
      'name': 'سارة أحمد',
      'department': 'متوسط',
      'grade': 'أول',
      'section': 'أ',
      'email': '<EMAIL>',
      'attendance': 95.0,
      'grades': {
        'الرياضيات': 92,
        'العلوم': 88,
        'اللغة العربية': 95,
        'اللغة الإنجليزية': 85,
      },
      'activities': ['نادي العلوم', 'فريق كرة القدم'],
      'parentName': 'أحمد محمد',
      'parentPhone': '0555111222',
    },
    {
      'id': 'M2B1002',
      'name': 'عبدالله محمد',
      'department': 'متوسط',
      'grade': 'ثاني',
      'section': 'ب',
      'email': '<EMAIL>',
      'attendance': 90.0,
      'grades': {
        'الرياضيات': 85,
        'العلوم': 90,
        'اللغة العربية': 88,
        'اللغة الإنجليزية': 82,
      },
      'activities': ['نادي الحاسب'],
      'parentName': 'محمد سالم',
      'parentPhone': '0555333444',
    },
    {
      'id': 'H1C1003',
      'name': 'نورا سالم',
      'department': 'ثانوي',
      'grade': 'أول',
      'section': 'ج',
      'email': '<EMAIL>',
      'attendance': 98.0,
      'grades': {
        'الرياضيات': 95,
        'الفيزياء': 92,
        'الكيمياء': 94,
        'اللغة العربية': 90,
        'اللغة الإنجليزية': 88,
      },
      'activities': ['نادي العلوم', 'فريق المناظرات'],
      'parentName': 'سالم عبدالله',
      'parentPhone': '0555555666',
    },
    {
      'id': 'H3A1004',
      'name': 'خالد عبدالله',
      'department': 'ثانوي',
      'grade': 'ثالث',
      'section': 'أ',
      'email': '<EMAIL>',
      'attendance': 85.0,
      'grades': {
        'الرياضيات': 80,
        'الفيزياء': 85,
        'الكيمياء': 82,
        'اللغة العربية': 88,
        'اللغة الإنجليزية': 75,
      },
      'activities': ['فريق كرة القدم'],
      'parentName': 'عبدالله سعيد',
      'parentPhone': '0555777888',
    },
  ];

  // بيانات النشاطات
  final List<Map<String, dynamic>> _activities = [
    {
      'id': '1',
      'name': 'نادي العلوم',
      'supervisor': 'أحمد محمود',
      'members': 25,
      'department': 'كلاهما',
      'meetingDays': ['الاثنين', 'الأربعاء'],
      'achievements': [
        'المركز الأول في مسابقة العلوم المحلية',
        'المشاركة في معرض الابتكار',
      ],
    },
    {
      'id': '2',
      'name': 'فريق كرة القدم',
      'supervisor': 'محمد سالم',
      'members': 22,
      'department': 'كلاهما',
      'meetingDays': ['الأحد', 'الثلاثاء', 'الخميس'],
      'achievements': ['المركز الثاني في بطولة المدارس'],
    },
    {
      'id': '3',
      'name': 'نادي الحاسب',
      'supervisor': 'عبدالرحمن علي',
      'members': 18,
      'department': 'ثانوي',
      'meetingDays': ['الاثنين', 'الخميس'],
      'achievements': ['تطوير تطبيق للمدرسة', 'المشاركة في هاكاثون البرمجة'],
    },
    {
      'id': '4',
      'name': 'فريق المناظرات',
      'supervisor': 'نورة محمد',
      'members': 15,
      'department': 'ثانوي',
      'meetingDays': ['الثلاثاء'],
      'achievements': ['المركز الأول في مسابقة المناظرات المحلية'],
    },
  ];

  // متغيرات للبحث والتصفية
  String _teacherSearchQuery = '';
  String? _selectedTeacherDepartment;

  String _studentSearchQuery = '';
  String? _selectedStudentDepartment;
  String? _selectedStudentGrade;
  String? _selectedStudentSection;

  String _activitySearchQuery = '';
  String? _selectedActivityDepartment;

  // بيانات التقارير
  int _totalStudents = 450;
  int _totalTeachers = 35;
  int _totalParents = 380;
  int _activeStudents = 430;

  // بيانات الحضور
  final Map<String, double> _attendanceData = {
    'الصف الأول متوسط': 92.5,
    'الصف الثاني متوسط': 88.7,
    'الصف الثالث متوسط': 90.2,
    'الصف الأول ثانوي': 85.3,
    'الصف الثاني ثانوي': 87.1,
    'الصف الثالث ثانوي': 91.8,
  };

  // بيانات الدرجات
  final Map<String, Map<String, double>> _gradesData = {
    'الرياضيات': {
      'ممتاز': 25.0,
      'جيد جداً': 35.0,
      'جيد': 20.0,
      'مقبول': 15.0,
      'ضعيف': 5.0,
    },
    'العلوم': {
      'ممتاز': 30.0,
      'جيد جداً': 40.0,
      'جيد': 15.0,
      'مقبول': 10.0,
      'ضعيف': 5.0,
    },
    'اللغة العربية': {
      'ممتاز': 20.0,
      'جيد جداً': 30.0,
      'جيد': 25.0,
      'مقبول': 20.0,
      'ضعيف': 5.0,
    },
    'اللغة الإنجليزية': {
      'ممتاز': 15.0,
      'جيد جداً': 25.0,
      'جيد': 30.0,
      'مقبول': 20.0,
      'ضعيف': 10.0,
    },
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadReportData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportData() async {
    setState(() => _isLoading = true);

    try {
      // في تطبيق حقيقي، ستقوم بجلب البيانات من قاعدة البيانات
      final users = await _userService.getAllUsers();

      setState(() {
        _totalStudents = users
            .where((user) => user.role == UserRole.student)
            .length;
        _totalTeachers = users
            .where((user) => user.role == UserRole.teacher)
            .length;
        _totalParents = users
            .where((user) => user.role == UserRole.parent)
            .length;
        _activeStudents = users
            .where((user) => user.role == UserRole.student && user.isActive)
            .length;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportReport(String reportType) async {
    setState(() => _isExporting = true);

    try {
      // محاكاة عملية التصدير
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير تقرير $reportType بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isExporting = false);
    }
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppConstants.primaryColor,
              onPrimary: AppConstants.textOnPrimaryColor,
              onSurface: AppConstants.textPrimaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });

      // إعادة تحميل البيانات بناءً على التاريخ الجديد
      _loadReportData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            tooltip: 'اختيار الفترة الزمنية',
            onPressed: () => _selectDateRange(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _loadReportData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color.fromARGB(210, 4, 42, 232),
          unselectedLabelColor: const Color.fromARGB(255, 18, 16, 16),
          indicatorColor: const Color.fromARGB(187, 3, 63, 111),
          isScrollable: true,
          tabs: const [
            Tab(text: 'تقرير المعلمين'),
            Tab(text: 'تقرير الطلاب'),
            Tab(text: 'الإحصائيات العامة'),
            Tab(text: 'تقرير النشاطات'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildTeachersReportTab(),
                _buildStudentsReportTab(),
                _buildStatisticsTab(),
                _buildActivitiesReportTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isExporting
            ? null
            : () => _exportReport(
                _getReportTypeFromTabIndex(_tabController.index),
              ),
        icon: _isExporting
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.download),
        label: Text(_isExporting ? 'جاري التصدير...' : 'تصدير التقرير'),
        backgroundColor: AppConstants.primaryColor,
      ),
    );
  }

  String _getReportTypeFromTabIndex(int index) {
    switch (index) {
      case 0:
        return 'المعلمين';
      case 1:
        return 'الطلاب';
      case 2:
        return 'الإحصائيات العامة';
      case 3:
        return 'النشاطات';
      default:
        return 'غير معروف';
    }
  }

  // تقرير المعلمين
  Widget _buildTeachersReportTab() {
    // تصفية المعلمين حسب البحث والقسم
    List<Map<String, dynamic>> filteredTeachers = _teachers.where((teacher) {
      final nameMatches = teacher['name'].toString().contains(
        _teacherSearchQuery,
      );
      final departmentMatches =
          _selectedTeacherDepartment == null ||
          teacher['department'] == _selectedTeacherDepartment ||
          (_selectedTeacherDepartment != 'كلاهما' &&
              teacher['department'] == 'كلاهما');
      return nameMatches && departmentMatches;
    }).toList();

    return Column(
      children: [
        // أدوات البحث والتصفية
        Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            children: [
              // حقل البحث
              TextField(
                decoration: const InputDecoration(
                  labelText: 'البحث عن معلم',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _teacherSearchQuery = value;
                  });
                },
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // اختيار القسم
              DropdownButtonFormField<String>(
                value: _selectedTeacherDepartment,
                decoration: const InputDecoration(
                  labelText: 'القسم',
                  prefixIcon: Icon(Icons.school),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('جميع الأقسام')),
                  DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
                  DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
                  DropdownMenuItem(value: 'كلاهما', child: Text('كلاهما')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedTeacherDepartment = value;
                  });
                },
              ),
            ],
          ),
        ),

        // قائمة المعلمين
        Expanded(
          child: filteredTeachers.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد نتائج',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  itemCount: filteredTeachers.length,
                  itemBuilder: (context, index) {
                    final teacher = filteredTeachers[index];
                    return _buildTeacherCard(teacher);
                  },
                ),
        ),
      ],
    );
  }

  // بطاقة المعلم
  // عرض تفاصيل المعلم
  void _showTeacherDetailsDialog(Map<String, dynamic> teacher) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رأس التقرير
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: AppConstants.successColor,
                        child: const Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              teacher['name'],
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeXLarge,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              teacher['subjects'].join(' - '),
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeLarge,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppConstants.paddingSmall,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getDepartmentColor(
                                  teacher['department'],
                                ).withAlpha(25),
                                borderRadius: BorderRadius.circular(
                                  AppConstants.radiusSmall,
                                ),
                              ),
                              child: Text(
                                _getDepartmentName(teacher['department']),
                                style: TextStyle(
                                  fontSize: AppConstants.fontSizeMedium,
                                  color: _getDepartmentColor(
                                    teacher['department'],
                                  ),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // معلومات الاتصال
                  const Text(
                    'معلومات الاتصال',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  _buildInfoRow(
                    'البريد الإلكتروني',
                    teacher['email'],
                    Icons.email,
                  ),
                  _buildInfoRow('رقم الهاتف', teacher['phone'], Icons.phone),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // تقدم الدروس
                  const Text(
                    'تقدم الدروس',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'الدروس المكتملة: ${teacher['lessonsCompleted']}/${teacher['lessonsTotal']}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                      Text(
                        '${((teacher['lessonsCompleted'] / teacher['lessonsTotal']) * 100).toStringAsFixed(1)}%',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                          color: _getCompletionColor(
                            (teacher['lessonsCompleted'] /
                                    teacher['lessonsTotal']) *
                                100,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  LinearProgressIndicator(
                    value:
                        teacher['lessonsCompleted'] / teacher['lessonsTotal'],
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getCompletionColor(
                        (teacher['lessonsCompleted'] /
                                teacher['lessonsTotal']) *
                            100,
                      ),
                    ),
                    minHeight: 10,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // الأيام التي لم يتم رفع الدروس فيها
                  const Text(
                    'الأيام التي لم يتم رفع الدروس فيها',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  teacher['missingDates'].isEmpty
                      ? const Text(
                          'لا توجد أيام متأخرة',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.successColor,
                          ),
                        )
                      : Column(
                          children: [
                            for (final date in teacher['missingDates'])
                              Padding(
                                padding: const EdgeInsets.only(
                                  bottom: AppConstants.paddingSmall,
                                ),
                                child: Row(
                                  children: [
                                    const Icon(
                                      Icons.warning,
                                      color: AppConstants.warningColor,
                                      size: 20,
                                    ),
                                    const SizedBox(
                                      width: AppConstants.paddingSmall,
                                    ),
                                    Text(
                                      date,
                                      style: const TextStyle(
                                        fontSize: AppConstants.fontSizeMedium,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // أزرار الإجراءات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.close),
                        label: const Text('إغلاق'),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _exportTeacherReport(teacher);
                        },
                        icon: const Icon(Icons.download),
                        label: const Text('تصدير التقرير'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // تصدير تقرير المعلم
  Future<void> _exportTeacherReport(Map<String, dynamic> teacher) async {
    setState(() => _isExporting = true);

    try {
      // محاكاة عملية التصدير
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير تقرير المعلم ${teacher['name']} بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isExporting = false);
    }
  }

  // الحصول على لون القسم
  Color _getDepartmentColor(String department) {
    switch (department) {
      case 'متوسط':
        return AppConstants.primaryColor;
      case 'ثانوي':
        return AppConstants.warningColor;
      case 'كلاهما':
        return AppConstants.successColor;
      default:
        return AppConstants.textSecondaryColor;
    }
  }

  // الحصول على اسم القسم
  String _getDepartmentName(String department) {
    switch (department) {
      case 'متوسط':
        return 'قسم المتوسط';
      case 'ثانوي':
        return 'قسم الثانوي';
      case 'كلاهما':
        return 'المتوسط والثانوي';
      default:
        return department;
    }
  }

  // الحصول على لون نسبة الإكمال
  Color _getCompletionColor(double percentage) {
    if (percentage >= 90) {
      return AppConstants.successColor;
    } else if (percentage >= 75) {
      return AppConstants.primaryColor;
    } else if (percentage >= 50) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.errorColor;
    }
  }

  // صف معلومات
  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppConstants.primaryColor),
          const SizedBox(width: AppConstants.paddingSmall),
          Text(
            '$label: ',
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTeacherCard(Map<String, dynamic> teacher) {
    final completionPercentage =
        (teacher['lessonsCompleted'] / teacher['lessonsTotal']) * 100;

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: InkWell(
        onTap: () => _showTeacherDetailsDialog(teacher),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppConstants.successColor,
                    child: const Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          teacher['name'],
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeLarge,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          teacher['subjects'].join(' - '),
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingSmall,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getDepartmentColor(
                              teacher['department'],
                            ).withAlpha(25),
                            borderRadius: BorderRadius.circular(
                              AppConstants.radiusSmall,
                            ),
                          ),
                          child: Text(
                            _getDepartmentName(teacher['department']),
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: _getDepartmentColor(teacher['department']),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Text(
                        '${teacher['lessonsCompleted']}/${teacher['lessonsTotal']}',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: _getCompletionColor(completionPercentage),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'الدروس المكتملة',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              LinearProgressIndicator(
                value: teacher['lessonsCompleted'] / teacher['lessonsTotal'],
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getCompletionColor(completionPercentage),
                ),
                minHeight: 8,
                borderRadius: BorderRadius.circular(4),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'آخر نشاط: ${teacher['lastActivity']}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                  Text(
                    'اضغط للتفاصيل',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تقرير الطلاب
  Widget _buildStudentsReportTab() {
    // تصفية الطلاب حسب البحث والقسم والصف والفصل
    List<Map<String, dynamic>> filteredStudents = _students.where((student) {
      final nameMatches = student['name'].toString().contains(
        _studentSearchQuery,
      );
      final departmentMatches =
          _selectedStudentDepartment == null ||
          student['department'] == _selectedStudentDepartment;
      final gradeMatches =
          _selectedStudentGrade == null ||
          student['grade'] == _selectedStudentGrade;
      final sectionMatches =
          _selectedStudentSection == null ||
          student['section'] == _selectedStudentSection;
      return nameMatches && departmentMatches && gradeMatches && sectionMatches;
    }).toList();

    return Column(
      children: [
        // أدوات البحث والتصفية
        Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            children: [
              // حقل البحث
              TextField(
                decoration: const InputDecoration(
                  labelText: 'البحث عن طالب',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _studentSearchQuery = value;
                  });
                },
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // صف التصفية
              Row(
                children: [
                  // القسم
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedStudentDepartment,
                      decoration: const InputDecoration(
                        labelText: 'القسم',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('الكل')),
                        DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
                        DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedStudentDepartment = value;
                          // إعادة تعيين الصف والفصل عند تغيير القسم
                          _selectedStudentGrade = null;
                          _selectedStudentSection = null;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),

                  // الصف
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedStudentGrade,
                      decoration: const InputDecoration(
                        labelText: 'الصف',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('الكل')),
                        DropdownMenuItem(value: 'أول', child: Text('أول')),
                        DropdownMenuItem(value: 'ثاني', child: Text('ثاني')),
                        DropdownMenuItem(value: 'ثالث', child: Text('ثالث')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedStudentGrade = value;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),

                  // الفصل
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedStudentSection,
                      decoration: const InputDecoration(
                        labelText: 'الفصل',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(value: null, child: Text('الكل')),
                        DropdownMenuItem(value: 'أ', child: Text('أ')),
                        DropdownMenuItem(value: 'ب', child: Text('ب')),
                        DropdownMenuItem(value: 'ج', child: Text('ج')),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedStudentSection = value;
                        });
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // قائمة الطلاب
        Expanded(
          child: filteredStudents.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد نتائج',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  itemCount: filteredStudents.length,
                  itemBuilder: (context, index) {
                    final student = filteredStudents[index];
                    return _buildStudentCard(student);
                  },
                ),
        ),
      ],
    );
  }

  // بطاقة الطالب
  Widget _buildStudentCard(Map<String, dynamic> student) {
    // حساب متوسط الدرجات
    double averageGrade = 0;
    if (student['grades'] != null && (student['grades'] as Map).isNotEmpty) {
      double sum = 0;
      final grades = student['grades'] as Map;
      for (final grade in grades.values) {
        sum += grade as double;
      }
      averageGrade = sum / grades.length;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: InkWell(
        onTap: () => _showStudentDetailsDialog(student),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppConstants.primaryColor,
                    child: const Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          student['name'],
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeLarge,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppConstants.paddingSmall,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getDepartmentColor(
                                  student['department'],
                                ).withAlpha(25),
                                borderRadius: BorderRadius.circular(
                                  AppConstants.radiusSmall,
                                ),
                              ),
                              child: Text(
                                student['department'],
                                style: TextStyle(
                                  fontSize: AppConstants.fontSizeSmall,
                                  color: _getDepartmentColor(
                                    student['department'],
                                  ),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${student['grade']} - ${student['section']}',
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeMedium,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        GestureDetector(
                          onTap: () {
                            Clipboard.setData(
                              ClipboardData(text: student['id']),
                            );
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('تم نسخ رقم الطالب إلى الحافظة'),
                                backgroundColor: AppConstants.successColor,
                                duration: Duration(seconds: 1),
                              ),
                            );
                          },
                          child: Row(
                            children: [
                              const Icon(
                                Icons.copy,
                                size: 16,
                                color: AppConstants.primaryColor,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'رقم الطالب: ${student['id']}',
                                style: const TextStyle(
                                  fontSize: AppConstants.fontSizeSmall,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.star, color: Colors.amber, size: 20),
                          const SizedBox(width: 4),
                          Text(
                            averageGrade.toStringAsFixed(1),
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.calendar_today,
                            color: AppConstants.successColor,
                            size: 20,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${student['attendance']}%',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.successColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'ولي الأمر: ${student['parentName']}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                  Text(
                    'اضغط للتفاصيل',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض تفاصيل الطالب
  void _showStudentDetailsDialog(Map<String, dynamic> student) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رأس التقرير
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: AppConstants.primaryColor,
                        child: const Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              student['name'],
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeXLarge,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${student['department']} - الصف ${student['grade']} - ${student['section']}',
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeLarge,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            GestureDetector(
                              onTap: () {
                                Clipboard.setData(
                                  ClipboardData(text: student['id']),
                                );
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'تم نسخ رقم الطالب إلى الحافظة',
                                    ),
                                    backgroundColor: AppConstants.successColor,
                                    duration: Duration(seconds: 1),
                                  ),
                                );
                              },
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.copy,
                                    size: 16,
                                    color: AppConstants.primaryColor,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'رقم الطالب: ${student['id']}',
                                    style: const TextStyle(
                                      fontSize: AppConstants.fontSizeMedium,
                                      color: AppConstants.primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // معلومات الاتصال
                  const Text(
                    'معلومات الاتصال',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  _buildInfoRow(
                    'البريد الإلكتروني',
                    student['email'],
                    Icons.email,
                  ),
                  _buildInfoRow(
                    'ولي الأمر',
                    student['parentName'],
                    Icons.family_restroom,
                  ),
                  _buildInfoRow(
                    'رقم هاتف ولي الأمر',
                    student['parentPhone'],
                    Icons.phone,
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // الحضور
                  const Text(
                    'نسبة الحضور',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'نسبة الحضور الإجمالية:',
                        style: TextStyle(fontSize: AppConstants.fontSizeMedium),
                      ),
                      Text(
                        '${student['attendance']}%',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                          color: _getAttendanceColor(student['attendance']),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingSmall),
                  LinearProgressIndicator(
                    value: student['attendance'] / 100,
                    backgroundColor: Colors.grey.shade200,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _getAttendanceColor(student['attendance']),
                    ),
                    minHeight: 10,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // الدرجات
                  const Text(
                    'الدرجات',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  if (student['grades'] != null &&
                      (student['grades'] as Map).isNotEmpty)
                    Column(
                      children: [
                        for (final entry in (student['grades'] as Map).entries)
                          _buildGradeItem(
                            entry.key.toString(),
                            entry.value as double,
                          ),
                      ],
                    )
                  else
                    const Text(
                      'لا توجد درجات متاحة',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // الأنشطة
                  const Text(
                    'الأنشطة',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  if (student['activities'] != null &&
                      (student['activities'] as List).isNotEmpty)
                    Wrap(
                      spacing: AppConstants.paddingSmall,
                      runSpacing: AppConstants.paddingSmall,
                      children: [
                        for (final activity in student['activities'])
                          Chip(
                            label: Text(activity),
                            backgroundColor: AppConstants.infoColor.withAlpha(
                              50,
                            ),
                            labelStyle: const TextStyle(
                              color: AppConstants.infoColor,
                            ),
                          ),
                      ],
                    )
                  else
                    const Text(
                      'لا توجد أنشطة متاحة',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // أزرار الإجراءات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.close),
                        label: const Text('إغلاق'),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _exportStudentReport(student);
                        },
                        icon: const Icon(Icons.download),
                        label: const Text('تصدير التقرير'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // تصدير تقرير الطالب
  Future<void> _exportStudentReport(Map<String, dynamic> student) async {
    setState(() => _isExporting = true);

    try {
      // محاكاة عملية التصدير
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير تقرير الطالب ${student['name']} بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isExporting = false);
    }
  }

  // عنصر درجة
  Widget _buildGradeItem(String subject, double grade) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                subject,
                style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
              ),
              Text(
                grade.toString(),
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                  color: _getGradeColor(grade),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: grade / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(_getGradeColor(grade)),
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
      ),
    );
  }

  // الحصول على لون الدرجة
  Color _getGradeColor(double grade) {
    if (grade >= 90) {
      return AppConstants.successColor;
    } else if (grade >= 80) {
      return AppConstants.primaryColor;
    } else if (grade >= 70) {
      return AppConstants.infoColor;
    } else if (grade >= 60) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.errorColor;
    }
  }

  // الحصول على لون الحضور
  Color _getAttendanceColor(double attendance) {
    if (attendance >= 90) {
      return AppConstants.successColor;
    } else if (attendance >= 80) {
      return AppConstants.primaryColor;
    } else if (attendance >= 70) {
      return AppConstants.warningColor;
    } else {
      return AppConstants.errorColor;
    }
  }

  // تقرير الإحصائيات
  Widget _buildStatisticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateRangeHeader(),
          const SizedBox(height: AppConstants.paddingLarge),

          // إحصائيات المستخدمين
          const Text(
            'إحصائيات المستخدمين',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: AppConstants.paddingMedium,
            mainAxisSpacing: AppConstants.paddingMedium,
            children: [
              _buildStatCard(
                'إجمالي الطلاب',
                _totalStudents.toString(),
                Icons.school,
                AppConstants.primaryColor,
              ),
              _buildStatCard(
                'إجمالي المعلمين',
                _totalTeachers.toString(),
                Icons.person,
                AppConstants.successColor,
              ),
              _buildStatCard(
                'إجمالي أولياء الأمور',
                _totalParents.toString(),
                Icons.family_restroom,
                AppConstants.warningColor,
              ),
              _buildStatCard(
                'الطلاب النشطين',
                '$_activeStudents من $_totalStudents',
                Icons.check_circle,
                AppConstants.infoColor,
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // نسبة الحضور
          const Text(
            'نسبة الحضور',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            ),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Column(
                children: [
                  for (final entry in _attendanceData.entries)
                    _buildProgressItem(
                      entry.key,
                      entry.value,
                      AppConstants.primaryColor,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // تقرير النشاطات
  Widget _buildActivitiesReportTab() {
    // تصفية النشاطات حسب البحث والقسم
    List<Map<String, dynamic>> filteredActivities = _activities.where((
      activity,
    ) {
      final nameMatches = activity['name'].toString().contains(
        _activitySearchQuery,
      );
      final departmentMatches =
          _selectedActivityDepartment == null ||
          activity['department'] == _selectedActivityDepartment ||
          activity['department'] == 'كلاهما';
      return nameMatches && departmentMatches;
    }).toList();

    return Column(
      children: [
        // أدوات البحث والتصفية
        Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            children: [
              // حقل البحث
              TextField(
                decoration: const InputDecoration(
                  labelText: 'البحث عن نشاط',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  setState(() {
                    _activitySearchQuery = value;
                  });
                },
              ),
              const SizedBox(height: AppConstants.paddingMedium),

              // اختيار القسم
              DropdownButtonFormField<String>(
                value: _selectedActivityDepartment,
                decoration: const InputDecoration(
                  labelText: 'القسم',
                  prefixIcon: Icon(Icons.school),
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: null, child: Text('جميع الأقسام')),
                  DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
                  DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
                  DropdownMenuItem(value: 'كلاهما', child: Text('كلاهما')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedActivityDepartment = value;
                  });
                },
              ),
            ],
          ),
        ),

        // قائمة النشاطات
        Expanded(
          child: filteredActivities.isEmpty
              ? const Center(
                  child: Text(
                    'لا توجد نتائج',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  itemCount: filteredActivities.length,
                  itemBuilder: (context, index) {
                    final activity = filteredActivities[index];
                    return _buildActivityCard(activity);
                  },
                ),
        ),
      ],
    );
  }

  // بطاقة النشاط
  Widget _buildActivityCard(Map<String, dynamic> activity) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: InkWell(
        onTap: () => _showActivityDetailsDialog(activity),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppConstants.infoColor,
                    child: const Icon(
                      Icons.sports,
                      size: 30,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          activity['name'],
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeLarge,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'المشرف: ${activity['supervisor']}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppConstants.paddingSmall,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: _getDepartmentColor(
                              activity['department'],
                            ).withAlpha(25),
                            borderRadius: BorderRadius.circular(
                              AppConstants.radiusSmall,
                            ),
                          ),
                          child: Text(
                            _getDepartmentName(activity['department']),
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: _getDepartmentColor(
                                activity['department'],
                              ),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      Row(
                        children: [
                          const Icon(
                            Icons.people,
                            color: AppConstants.primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            activity['members'].toString(),
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'عدد الأعضاء',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'أيام اللقاء: ${(activity['meetingDays'] as List).join(' - ')}',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondaryColor,
                    ),
                  ),
                  Text(
                    'اضغط للتفاصيل',
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض تفاصيل النشاط
  void _showActivityDetailsDialog(Map<String, dynamic> activity) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // رأس التقرير
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: AppConstants.infoColor,
                        child: const Icon(
                          Icons.sports,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              activity['name'],
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeXLarge,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'المشرف: ${activity['supervisor']}',
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeLarge,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppConstants.paddingSmall,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getDepartmentColor(
                                  activity['department'],
                                ).withAlpha(25),
                                borderRadius: BorderRadius.circular(
                                  AppConstants.radiusSmall,
                                ),
                              ),
                              child: Text(
                                _getDepartmentName(activity['department']),
                                style: TextStyle(
                                  fontSize: AppConstants.fontSizeMedium,
                                  color: _getDepartmentColor(
                                    activity['department'],
                                  ),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // معلومات النشاط
                  const Text(
                    'معلومات النشاط',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  _buildInfoRow(
                    'عدد الأعضاء',
                    activity['members'].toString(),
                    Icons.people,
                  ),
                  _buildInfoRow(
                    'أيام اللقاء',
                    (activity['meetingDays'] as List).join(' - '),
                    Icons.calendar_today,
                  ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // الإنجازات
                  const Text(
                    'الإنجازات',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeLarge,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  if (activity['achievements'] != null &&
                      (activity['achievements'] as List).isNotEmpty)
                    Column(
                      children: [
                        for (final achievement in activity['achievements'])
                          Padding(
                            padding: const EdgeInsets.only(
                              bottom: AppConstants.paddingSmall,
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Icon(
                                  Icons.emoji_events,
                                  color: Colors.amber,
                                  size: 20,
                                ),
                                const SizedBox(
                                  width: AppConstants.paddingSmall,
                                ),
                                Expanded(
                                  child: Text(
                                    achievement,
                                    style: const TextStyle(
                                      fontSize: AppConstants.fontSizeMedium,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    )
                  else
                    const Text(
                      'لا توجد إنجازات متاحة',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  const SizedBox(height: AppConstants.paddingLarge),

                  // أزرار الإجراءات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.close),
                        label: const Text('إغلاق'),
                      ),
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _exportActivityReport(activity);
                        },
                        icon: const Icon(Icons.download),
                        label: const Text('تصدير التقرير'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // تصدير تقرير النشاط
  Future<void> _exportActivityReport(Map<String, dynamic> activity) async {
    setState(() => _isExporting = true);

    try {
      // محاكاة عملية التصدير
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تصدير تقرير نشاط ${activity['name']} بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير التقرير: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isExporting = false);
    }
  }

  Widget _buildDateRangeHeader() {
    final dateFormat = DateFormat('yyyy/MM/dd');
    return GestureDetector(
      onTap: () => _selectDateRange(context),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Row(
            children: [
              const Icon(Icons.date_range, color: AppConstants.primaryColor),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الفترة الزمنية للتقرير',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'من ${dateFormat.format(_startDate)} إلى ${dateFormat.format(_endDate)}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_drop_down),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: color),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              title,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              value,
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressItem(String label, double value, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingSmall),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(fontSize: AppConstants.fontSizeMedium),
              ),
              Text(
                '${value.toStringAsFixed(1)}%',
                style: TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: value / 100,
            backgroundColor: Colors.grey.shade200,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 8,
            borderRadius: BorderRadius.circular(4),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
        ],
      ),
    );
  }
}
