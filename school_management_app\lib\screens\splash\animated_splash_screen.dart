import 'package:flutter/material.dart';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'dart:async';
import 'dart:math' as math;

import '../auth/login_screen.dart';

class AnimatedSplashScreen extends StatefulWidget {
  const AnimatedSplashScreen({super.key});

  @override
  State<AnimatedSplashScreen> createState() => _AnimatedSplashScreenState();
}

// فئة الجسيمات المتحركة في الخلفية
class Particle {
  Offset position;
  double size;
  double opacity;
  double speed;
  double angle;

  Particle({
    required this.position,
    required this.size,
    required this.opacity,
    required this.speed,
    required this.angle,
  });

  void update() {
    final dx = math.cos(angle) * speed;
    final dy = math.sin(angle) * speed;
    position = Offset(position.dx + dx, position.dy + dy);
  }
}

class _AnimatedSplashScreenState extends State<AnimatedSplashScreen>
    with TickerProviderStateMixin {
  // Animation controllers
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _backgroundController;
  late AnimationController _particleController;
  late AnimationController _pulseController;

  // Animations
  late Animation<double> _logoAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _pulseAnimation;

  // State flags
  bool _showText = false;
  bool _showTagline = false;
  bool _showParticles = false;

  // Particles for background effect
  final List<Particle> _particles = [];
  final int _particleCount = 30;
  final math.Random _random = math.Random();

  @override
  void initState() {
    super.initState();

    // Initialize particles
    _initializeParticles();

    // Logo animation controller
    _logoController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1800),
    );

    // Text animation controller
    _textController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // Background animation controller
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    )..repeat(reverse: true);

    // Particle animation controller
    _particleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 10000),
    )..repeat();

    // Pulse animation controller for logo
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    // Logo scale animation with bounce effect
    _logoAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    // Fade animation for text
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _textController, curve: Curves.easeIn));

    // Background color animation
    _backgroundAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _backgroundController, curve: Curves.easeInOut),
    );

    // Pulse animation for logo glow effect
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    // Start animations sequence
    _startAnimations();
  }

  void _initializeParticles() {
    for (int i = 0; i < _particleCount; i++) {
      _particles.add(
        Particle(
          position: Offset(
            _random.nextDouble() * 400,
            _random.nextDouble() * 800,
          ),
          size: _random.nextDouble() * 15 + 5,
          opacity: _random.nextDouble() * 0.6 + 0.2,
          speed: _random.nextDouble() * 2 + 0.5,
          angle: _random.nextDouble() * 2 * math.pi,
        ),
      );
    }
  }

  void _startAnimations() async {
    // Show particles immediately
    setState(() {
      _showParticles = true;
    });

    // Start background animation
    _backgroundController.forward();

    // Wait a bit before starting logo animation
    await Future.delayed(const Duration(milliseconds: 500));

    // Start logo animation
    await _logoController.forward();

    // Start pulse animation for logo
    _pulseController.forward();

    // Show text after logo animation
    setState(() {
      _showText = true;
    });

    // Start text fade animation
    await _textController.forward();

    // Show tagline after text animation
    setState(() {
      _showTagline = true;
    });

    // Navigate to login screen after delay
    Timer(const Duration(milliseconds: 4000), () {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    });
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    _backgroundController.dispose();
    _particleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Update particles position
    if (_showParticles) {
      for (var particle in _particles) {
        particle.update();

        // Reset particles that go off screen
        if (particle.position.dy > MediaQuery.of(context).size.height) {
          particle.position = Offset(
            _random.nextDouble() * MediaQuery.of(context).size.width,
            -particle.size,
          );
        }
        if (particle.position.dx > MediaQuery.of(context).size.width) {
          particle.position = Offset(
            -particle.size,
            _random.nextDouble() * MediaQuery.of(context).size.height,
          );
        }
      }
    }

    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        // Animated gradient background color
        final Color startColor = Color.fromARGB(255, 4, 42, 73);
        final Color endColor = Color.fromARGB(255, 8, 83, 148);

        return Scaffold(
          backgroundColor: Color.lerp(
            startColor,
            endColor,
            _backgroundAnimation.value,
          ),
          body: Stack(
            children: [
              // Animated particles in background
              if (_showParticles)
                AnimatedBuilder(
                  animation: _particleController,
                  builder: (context, _) {
                    return CustomPaint(
                      size: Size(
                        MediaQuery.of(context).size.width,
                        MediaQuery.of(context).size.height,
                      ),
                      painter: ParticlePainter(particles: _particles),
                    );
                  },
                ),

              // Main content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo with scale and pulse animations
                    AnimatedBuilder(
                      animation: _pulseController,
                      builder: (context, child) {
                        return ScaleTransition(
                          scale: _logoAnimation,
                          child: Transform.scale(
                            scale: _pulseAnimation.value,
                            child: Container(
                              width: 180,
                              height: 180,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.blue.withAlpha(150),
                                    blurRadius: 20 * _pulseAnimation.value,
                                    spreadRadius: 5 * _pulseAnimation.value,
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withAlpha(51),
                                    blurRadius: 10,
                                    offset: const Offset(0, 5),
                                  ),
                                ],
                              ),
                              padding: const EdgeInsets.all(15),
                              child: Image.asset(
                                'assets/images/l.jpeg',
                                fit: BoxFit.contain,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 40),

                    // App name with typewriter animation
                    if (_showText)
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: ShaderMask(
                          shaderCallback: (bounds) {
                            return LinearGradient(
                              colors: [
                                Colors.white,
                                Colors.lightBlueAccent,
                                Colors.white,
                              ],
                              stops: [0.0, 0.5, 1.0],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ).createShader(bounds);
                          },
                          child: DefaultTextStyle(
                            style: const TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontFamily: 'Cairo',
                            ),
                            child: AnimatedTextKit(
                              animatedTexts: [
                                TypewriterAnimatedText(
                                  'مهامي',
                                  speed: const Duration(milliseconds: 100),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                              isRepeatingAnimation: false,
                              totalRepeatCount: 1,
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 20),

                    // Tagline with fade animation
                    if (_showTagline)
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: DefaultTextStyle(
                          style: const TextStyle(
                            fontSize: 20,
                            color: Colors.white,
                            fontFamily: 'Cairo',
                            letterSpacing: 1.2,
                          ),
                          child: AnimatedTextKit(
                            animatedTexts: [
                              FadeAnimatedText(
                                'الحل المتكامل لإدارة المدارس',
                                duration: const Duration(milliseconds: 1500),
                                textAlign: TextAlign.center,
                              ),
                            ],
                            isRepeatingAnimation: false,
                            totalRepeatCount: 1,
                          ),
                        ),
                      ),

                    // Loading indicator with animation
                    if (_showTagline)
                      Padding(
                        padding: const EdgeInsets.only(top: 50),
                        child: TweenAnimationBuilder<double>(
                          tween: Tween<double>(begin: 0.0, end: 1.0),
                          duration: const Duration(milliseconds: 1000),
                          builder: (context, value, child) {
                            return Opacity(
                              opacity: value,
                              child: SizedBox(
                                width: 40,
                                height: 40,
                                child: CircularProgressIndicator(
                                  color: Colors.white.withAlpha(179),
                                  strokeWidth: 3,
                                  value: null, // Indeterminate
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// رسام الجسيمات المتحركة
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;

  ParticlePainter({required this.particles});

  @override
  void paint(Canvas canvas, Size size) {
    for (var particle in particles) {
      final paint = Paint()
        ..color = Colors.white.withAlpha((particle.opacity * 255).toInt())
        ..style = PaintingStyle.fill;

      canvas.drawCircle(particle.position, particle.size, paint);
    }
  }

  @override
  bool shouldRepaint(ParticlePainter oldDelegate) => true;
}
