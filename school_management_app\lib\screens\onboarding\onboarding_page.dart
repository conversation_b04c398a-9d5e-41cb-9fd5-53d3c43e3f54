import 'package:flutter/material.dart';

// نموذج بيانات صفحة الترحيب
class OnboardingPageData {
  final String title;
  final String description;
  final String imagePath;
  final Color backgroundColor;
  final Color textColor;
  final Color accentColor;

  OnboardingPageData({
    required this.title,
    required this.description,
    required this.imagePath,
    required this.backgroundColor,
    required this.textColor,
    required this.accentColor,
  });
}

class OnboardingPage extends StatelessWidget {
  final OnboardingPageData data;

  const OnboardingPage({super.key, required this.data});

  // دالة لاختيار الأيقونة المناسبة حسب اسم الملف
  IconData _getIconForPage(String imagePath) {
    if (imagePath.contains('Learning')) {
      return Icons.school;
    } else if (imagePath.contains('Raising')) {
      return Icons.pan_tool;
    } else if (imagePath.contains('Grades')) {
      return Icons.emoji_events;
    }
    return Icons.school;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Container(
      color: data.backgroundColor,
      child: Stack(
        children: [
          // الشكل المنحني للخلفية
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: size.width * 0.15,
            child: Container(color: data.accentColor),
          ),

          // المنحنى الجانبي
          Positioned(
            right: size.width * 0.05,
            top: size.height * 0.2,
            bottom: size.height * 0.2,
            width: size.width * 0.2,
            child: CustomPaint(
              size: Size(size.width * 0.2, size.height * 0.6),
              painter: CurvePainter(curveColor: data.backgroundColor),
            ),
          ),

          // محتوى الصفحة
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: size.height * 0.1),

                  // اسم التطبيق بخط كبير
                  Text(
                    'مهامي',
                    style: TextStyle(
                      color: data.textColor,
                      fontSize: 36,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                  ),

                  // العنوان
                  Text(
                    data.title,
                    style: TextStyle(
                      color: data.textColor,
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Cairo',
                    ),
                  ),

                  const SizedBox(height: 10),

                  // سهم للتنقل
                  Icon(
                    Icons.arrow_downward_rounded,
                    color: data.textColor.withAlpha(180),
                    size: 30,
                  ),

                  const Spacer(),

                  // الصورة التوضيحية
                  Center(
                    child: Container(
                      height: size.height * 0.35,
                      width: size.height * 0.35,
                      decoration: BoxDecoration(
                        color: data.textColor.withAlpha(20),
                        borderRadius: BorderRadius.circular(
                          size.height * 0.175,
                        ),
                      ),
                      child: Icon(
                        _getIconForPage(data.imagePath),
                        size: size.height * 0.15,
                        color: data.textColor,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // وصف الصفحة
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 15,
                      horizontal: 20,
                    ),
                    decoration: BoxDecoration(
                      color: data.accentColor.withAlpha(40),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Text(
                      data.description,
                      style: TextStyle(
                        color: data.textColor,
                        fontSize: 16,
                        fontFamily: 'Cairo',
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  SizedBox(height: size.height * 0.15),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// رسام المنحنى الجانبي
class CurvePainter extends CustomPainter {
  final Color curveColor;

  CurvePainter({required this.curveColor});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = curveColor
      ..style = PaintingStyle.fill;

    final path = Path();

    // رسم المنحنى
    path.moveTo(0, 0);
    path.lineTo(size.width, size.height * 0.2);
    path.lineTo(size.width, size.height * 0.8);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
