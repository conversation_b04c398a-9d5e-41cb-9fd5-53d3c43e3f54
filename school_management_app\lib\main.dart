import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'providers/auth_provider.dart';
import 'providers/theme_provider.dart';
import 'screens/auth/login_screen.dart';
import 'screens/admin/admin_dashboard.dart';
import 'screens/teacher/teacher_dashboard.dart';
import 'screens/student/student_dashboard.dart';
import 'screens/parent/parent_dashboard.dart';
import 'screens/onboarding/onboarding_screen.dart';
import 'models/user.dart';
import 'models/student.dart';
import 'models/teacher.dart';
import 'models/assignment.dart';
import 'models/grade.dart';
import 'models/schedule.dart';
import 'utils/constants.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive Adapters
  Hive.registerAdapter(UserRoleAdapter());
  Hive.registerAdapter(UserAdapter());
  Hive.registerAdapter(StudentAdapter());
  Hive.registerAdapter(TeacherAdapter());
  Hive.registerAdapter(AssignmentStatusAdapter());
  Hive.registerAdapter(AssignmentAdapter());
  Hive.registerAdapter(GradeAdapter());
  Hive.registerAdapter(ScheduleItemAdapter());

  runApp(const SchoolManagementApp());
}

class SchoolManagementApp extends StatelessWidget {
  const SchoolManagementApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()),
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'نظام إدارة المدرسة',
            debugShowCheckedModeBanner: false,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            themeMode: themeProvider.themeMode,

            // Localization
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', 'SA'), // Arabic
              Locale('en', 'US'), // English
            ],
            locale: const Locale('ar', 'SA'),

            // Routes
            home: const AuthWrapper(),
            routes: {
              '/login': (context) => const LoginScreen(),
              '/admin': (context) => const AdminDashboard(),
              '/teacher': (context) => const TeacherDashboard(),
              '/student': (context) => const StudentDashboard(),
              '/parent': (context) => const ParentDashboard(),
            },
          );
        },
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    // Show onboarding screen first
    return const OnboardingScreen();
  }
}
