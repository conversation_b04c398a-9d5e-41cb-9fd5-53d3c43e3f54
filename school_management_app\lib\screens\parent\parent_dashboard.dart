import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_app_bar.dart';
import '../auth/login_screen.dart';

class ParentDashboard extends StatefulWidget {
  const ParentDashboard({super.key});

  @override
  State<ParentDashboard> createState() => _ParentDashboardState();
}

class _ParentDashboardState extends State<ParentDashboard> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const ParentHomeScreen(),
    const ParentChildrenScreen(),
    const ParentProgressScreen(),
    const ParentCommunicationScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'لوحة تحكم ولي الأمر',
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
          ),
        ],
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.family_restroom),
            label: 'الأطفال',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.analytics), label: 'التقدم'),
          BottomNavigationBarItem(icon: Icon(Icons.message), label: 'التواصل'),
        ],
      ),
    );
  }

  Future<void> _logout(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }
}

class ParentHomeScreen extends StatelessWidget {
  const ParentHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Section
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingLarge),
                  child: Row(
                    children: [
                      const CircleAvatar(
                        radius: 30,
                        backgroundColor: AppConstants.warningColor,
                        child: Icon(
                          Icons.family_restroom,
                          size: 30,
                          color: AppConstants.textOnPrimaryColor,
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مرحباً، ${authProvider.currentUser?.name ?? 'ولي الأمر'}',
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeXLarge,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Text(
                              'متابعة تقدم الأطفال',
                              style: TextStyle(
                                fontSize: AppConstants.fontSizeMedium,
                                color: AppConstants.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // Children Overview
          const Text(
            'نظرة عامة على الأطفال',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          // Children Cards
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 2, // Mock data for 2 children
            itemBuilder: (context, index) {
              return Card(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingMedium,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: index == 0
                                ? AppConstants.primaryColor
                                : AppConstants.successColor,
                            child: Text(
                              index == 0 ? 'س' : 'ع',
                              style: const TextStyle(
                                color: AppConstants.textOnPrimaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppConstants.paddingMedium),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  index == 0 ? 'سارة أحمد' : 'عبدالله محمد',
                                  style: const TextStyle(
                                    fontSize: AppConstants.fontSizeLarge,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  index == 0
                                      ? 'الصف العاشر أ'
                                      : 'الصف التاسع ب',
                                  style: const TextStyle(
                                    fontSize: AppConstants.fontSizeMedium,
                                    color: AppConstants.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {},
                            icon: const Icon(Icons.arrow_forward_ios),
                          ),
                        ],
                      ),

                      const SizedBox(height: AppConstants.paddingMedium),

                      // Quick Stats
                      Row(
                        children: [
                          Expanded(
                            child: _buildStatItem(
                              'الواجبات',
                              index == 0 ? '12/15' : '8/10',
                              Icons.assignment,
                              AppConstants.primaryColor,
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'المعدل',
                              index == 0 ? '85%' : '78%',
                              Icons.grade,
                              AppConstants.successColor,
                            ),
                          ),
                          Expanded(
                            child: _buildStatItem(
                              'الحضور',
                              index == 0 ? '92%' : '88%',
                              Icons.check_circle,
                              AppConstants.infoColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: AppConstants.paddingLarge),

          // Recent Activities
          const Text(
            'الأنشطة الحديثة',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 4,
            itemBuilder: (context, index) {
              final activities = [
                {
                  'title': 'تم تسليم واجب الرياضيات',
                  'subtitle': 'سارة أحمد - منذ ساعتين',
                  'icon': Icons.assignment_turned_in,
                  'color': AppConstants.successColor,
                },
                {
                  'title': 'درجة جديدة في الفيزياء',
                  'subtitle': 'عبدالله محمد - منذ 4 ساعات',
                  'icon': Icons.grade,
                  'color': AppConstants.primaryColor,
                },
                {
                  'title': 'واجب جديد في الكيمياء',
                  'subtitle': 'سارة أحمد - أمس',
                  'icon': Icons.assignment,
                  'color': AppConstants.warningColor,
                },
                {
                  'title': 'حضور ممتاز هذا الأسبوع',
                  'subtitle': 'عبدالله محمد - منذ يومين',
                  'icon': Icons.star,
                  'color': AppConstants.infoColor,
                },
              ];

              final activity = activities[index];

              return Card(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingSmall,
                ),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: (activity['color'] as Color).withOpacity(
                      0.1,
                    ),
                    child: Icon(
                      activity['icon'] as IconData,
                      color: activity['color'] as Color,
                    ),
                  ),
                  title: Text(activity['title'] as String),
                  subtitle: Text(activity['subtitle'] as String),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: AppConstants.paddingSmall),
        Text(
          value,
          style: TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textSecondaryColor,
          ),
        ),
      ],
    );
  }
}

class ParentChildrenScreen extends StatelessWidget {
  const ParentChildrenScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'تفاصيل الأطفال',
        style: TextStyle(fontSize: AppConstants.fontSizeXLarge),
      ),
    );
  }
}

class ParentProgressScreen extends StatelessWidget {
  const ParentProgressScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'تقارير التقدم',
        style: TextStyle(fontSize: AppConstants.fontSizeXLarge),
      ),
    );
  }
}

class ParentCommunicationScreen extends StatelessWidget {
  const ParentCommunicationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'التواصل مع المدرسة',
        style: TextStyle(fontSize: AppConstants.fontSizeXLarge),
      ),
    );
  }
}
