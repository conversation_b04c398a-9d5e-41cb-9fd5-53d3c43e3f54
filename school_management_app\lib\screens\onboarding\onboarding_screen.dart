import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../auth/login_screen.dart';
import 'onboarding_page.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  bool _isLastPage = false;

  // تعريف صفحات الترحيب
  final List<OnboardingPageData> _pages = [
    OnboardingPageData(
      title: 'استكشف عالم المعرفة',
      description: 'اكتشف مواد دراسية متنوعة وتعلم بطريقة ممتعة وتفاعلية',
      imagePath: 'assets/images/onboarding/Learning-cuate.svg',
      backgroundColor: const Color.fromARGB(
        255,
        181,
        131,
        17,
      ), // AppTheme.secondaryColor
      textColor: Colors.white,
      accentColor: const Color.fromARGB(
        162,
        2,
        35,
        118,
      ), // AppTheme.primaryColor
    ),
    OnboardingPageData(
      title: 'شارك في الفصل',
      description: 'ارفع يدك للمشاركة وتفاعل مع زملائك ومعلميك',
      imagePath: 'assets/images/onboarding/Raising hand-cuate.svg',
      backgroundColor: const Color.fromARGB(
        186,
        1,
        35,
        121,
      ), // AppTheme.primaryColor
      textColor: Colors.white,
      accentColor: const Color(0xFFFFD166), // AppTheme.secondaryColor
    ),
    OnboardingPageData(
      title: 'حقق النجاح',
      description: 'احصل على أفضل الدرجات وتابع تقدمك الدراسي',
      imagePath: 'assets/images/onboarding/Grades-cuate.svg',
      backgroundColor: const Color.fromARGB(
        255,
        219,
        7,
        7,
      ), // AppTheme.accentColor
      textColor: Colors.white,
      accentColor: const Color(0xFFFFD166), // AppTheme.secondaryColor
    ),
  ];

  @override
  void initState() {
    super.initState();
    // تعيين وضع الشاشة للعرض بملء الشاشة
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  @override
  void dispose() {
    // إعادة وضع الشاشة إلى الوضع الطبيعي
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.manual,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
    );
    _pageController.dispose();
    super.dispose();
  }

  void _onNextPressed() {
    if (_isLastPage) {
      _navigateToLogin();
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  void _navigateToLogin() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // صفحات الترحيب
          PageView.builder(
            controller: _pageController,
            itemCount: _pages.length,
            onPageChanged: (index) {
              setState(() {
                _isLastPage = index == _pages.length - 1;
              });
            },
            itemBuilder: (context, index) {
              return OnboardingPage(data: _pages[index]);
            },
          ),

          // مؤشرات الصفحات
          Positioned(
            bottom: 30,
            left: 0,
            right: 0,
            child: Center(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  _pages.length,
                  (index) => AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    height: 8,
                    width:
                        _pageController.hasClients &&
                            _pageController.page?.round() == index
                        ? 24
                        : 8,
                    decoration: BoxDecoration(
                      color:
                          _pageController.hasClients &&
                              _pageController.page?.round() == index
                          ? Colors.white
                          : Colors.white.withAlpha(128),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // زر التخطي
          Positioned(
            top: 50,
            left: 20,
            child: SafeArea(
              child: TextButton(
                onPressed: _navigateToLogin,
                child: const Text(
                  'تخطي',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),

          // زر التالي
          Positioned(
            bottom: 80,
            right: 20,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              width: _isLastPage ? 120 : 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(30),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26), // 0.1 opacity
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: _isLastPage
                  ? TextButton.icon(
                      icon: Icon(
                        Icons.check,
                        color:
                            _pages[_pageController.hasClients
                                    ? _pageController.page?.round() ?? 0
                                    : 0]
                                .backgroundColor,
                      ),
                      label: Text(
                        'ابدأ',
                        style: TextStyle(
                          color:
                              _pages[_pageController.hasClients
                                      ? _pageController.page?.round() ?? 0
                                      : 0]
                                  .backgroundColor,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Cairo',
                        ),
                      ),
                      onPressed: _onNextPressed,
                    )
                  : IconButton(
                      icon: Icon(
                        Icons.arrow_forward,
                        color:
                            _pages[_pageController.hasClients
                                    ? _pageController.page?.round() ?? 0
                                    : 0]
                                .backgroundColor,
                      ),
                      onPressed: _onNextPressed,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
