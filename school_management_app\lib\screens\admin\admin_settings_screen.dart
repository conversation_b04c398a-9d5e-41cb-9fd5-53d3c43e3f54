import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  bool _darkMode = false;
  bool _arabicLanguage = true;
  bool _notificationsEnabled = true;
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Settings Header
          const Text(
            'الإعدادات',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Profile Section
          _buildProfileSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // App Settings
          _buildSettingsSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // System Settings
          _buildSystemSection(),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // About Section
          _buildAboutSection(),
        ],
      ),
    );
  }
  
  Widget _buildProfileSection() {
    final authProvider = Provider.of<AuthProvider>(context);
    final user = authProvider.currentUser;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملف الشخصي',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            Row(
              children: [
                const CircleAvatar(
                  radius: 40,
                  backgroundColor: AppConstants.primaryColor,
                  child: Icon(
                    Icons.person,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user?.name ?? 'المدير',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user?.email ?? '<EMAIL>',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'مدير النظام',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          color: AppConstants.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('ميزة تعديل الملف الشخصي قيد التطوير'),
                      backgroundColor: AppConstants.infoColor,
                    ),
                  );
                },
                icon: const Icon(Icons.edit),
                label: const Text('تعديل الملف الشخصي'),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSettingsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات التطبيق',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            SwitchListTile(
              title: const Text('الوضع الداكن'),
              subtitle: const Text('تفعيل الوضع الداكن للتطبيق'),
              value: _darkMode,
              onChanged: (value) {
                setState(() {
                  _darkMode = value;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة الوضع الداكن قيد التطوير'),
                    backgroundColor: AppConstants.infoColor,
                  ),
                );
              },
            ),
            
            SwitchListTile(
              title: const Text('اللغة العربية'),
              subtitle: const Text('استخدام اللغة العربية كلغة افتراضية'),
              value: _arabicLanguage,
              onChanged: (value) {
                setState(() {
                  _arabicLanguage = value;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة تغيير اللغة قيد التطوير'),
                    backgroundColor: AppConstants.infoColor,
                  ),
                );
              },
            ),
            
            SwitchListTile(
              title: const Text('الإشعارات'),
              subtitle: const Text('تفعيل إشعارات التطبيق'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث إعدادات الإشعارات'),
                    backgroundColor: AppConstants.successColor,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSystemSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات النظام',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            ListTile(
              leading: const Icon(Icons.backup, color: AppConstants.primaryColor),
              title: const Text('النسخ الاحتياطي'),
              subtitle: const Text('إنشاء نسخة احتياطية من البيانات'),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة النسخ الاحتياطي قيد التطوير'),
                    backgroundColor: AppConstants.infoColor,
                  ),
                );
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.restore, color: AppConstants.warningColor),
              title: const Text('استعادة البيانات'),
              subtitle: const Text('استعادة البيانات من نسخة احتياطية'),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة استعادة البيانات قيد التطوير'),
                    backgroundColor: AppConstants.infoColor,
                  ),
                );
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.delete_forever, color: AppConstants.errorColor),
              title: const Text('مسح البيانات'),
              subtitle: const Text('مسح جميع البيانات وإعادة ضبط النظام'),
              onTap: () {
                _showResetConfirmation();
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'حول التطبيق',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            const ListTile(
              leading: Icon(Icons.info, color: AppConstants.infoColor),
              title: Text('نظام إدارة المدرسة'),
              subtitle: Text('الإصدار 1.0.0'),
            ),
            
            const Divider(),
            
            ListTile(
              leading: const Icon(Icons.help, color: AppConstants.successColor),
              title: const Text('المساعدة والدعم'),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة المساعدة قيد التطوير'),
                    backgroundColor: AppConstants.infoColor,
                  ),
                );
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.privacy_tip, color: AppConstants.warningColor),
              title: const Text('سياسة الخصوصية'),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('ميزة سياسة الخصوصية قيد التطوير'),
                    backgroundColor: AppConstants.infoColor,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إعادة الضبط'),
        content: const Text(
          'هل أنت متأكد من رغبتك في مسح جميع البيانات وإعادة ضبط النظام؟ هذا الإجراء لا يمكن التراجع عنه.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إعادة ضبط النظام بنجاح'),
                  backgroundColor: AppConstants.successColor,
                ),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: AppConstants.errorColor,
            ),
            child: const Text('إعادة الضبط'),
          ),
        ],
      ),
    );
  }
}
