import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/user.dart';
import '../../utils/constants.dart';
import '../../services/user_service.dart';

class UserDetailsScreen extends StatefulWidget {
  final User user;

  const UserDetailsScreen({super.key, required this.user});

  @override
  State<UserDetailsScreen> createState() => _UserDetailsScreenState();
}

class _UserDetailsScreenState extends State<UserDetailsScreen> {
  final UserService _userService = UserService();
  final _formKey = GlobalKey<FormState>();
  
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late TextEditingController _passwordController;
  
  bool _isEditing = false;
  bool _isLoading = false;
  bool _showPassword = false;
  
  // إضافة متغيرات للطلاب
  String? _selectedDepartment;
  String? _selectedGrade;
  String? _selectedSection;
  String? _studentId;
  
  // إضافة متغيرات للمعلمين
  String? _selectedTeacherDepartment;
  List<String> _selectedSubjects = [];
  
  // إضافة متغيرات لأولياء الأمور
  List<String> _linkedStudentIds = [];
  
  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.user.name);
    _emailController = TextEditingController(text: widget.user.email);
    _phoneController = TextEditingController(text: widget.user.phoneNumber ?? '');
    _passwordController = TextEditingController(text: widget.user.password);
    
    // تهيئة البيانات حسب نوع المستخدم
    _initUserSpecificData();
  }
  
  void _initUserSpecificData() {
    // هذه الدالة ستقوم بتهيئة البيانات الخاصة بكل نوع مستخدم
    // في تطبيق حقيقي، ستقوم بجلب هذه البيانات من قاعدة البيانات
    
    switch (widget.user.role) {
      case UserRole.student:
        // بيانات افتراضية للطالب
        _selectedDepartment = 'متوسط';
        _selectedGrade = 'أول';
        _selectedSection = 'أ';
        _studentId = widget.user.id;
        break;
        
      case UserRole.teacher:
        // بيانات افتراضية للمعلم
        _selectedTeacherDepartment = 'متوسط';
        _selectedSubjects = ['الرياضيات', 'العلوم'];
        break;
        
      case UserRole.parent:
        // بيانات افتراضية لولي الأمر
        _linkedStudentIds = ['S12345', 'S67890'];
        break;
        
      case UserRole.admin:
        // لا توجد بيانات إضافية للمدير
        break;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
  
  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() => _isLoading = true);
    
    try {
      // تحديث بيانات المستخدم
      final updatedUser = widget.user.copyWith(
        name: _nameController.text,
        email: _emailController.text,
        phoneNumber: _phoneController.text,
        password: _passwordController.text,
        updatedAt: DateTime.now(),
      );
      
      // في تطبيق حقيقي، ستقوم بحفظ البيانات الخاصة بكل نوع مستخدم أيضاً
      
      await _userService.updateUser(updatedUser);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ البيانات بنجاح'),
            backgroundColor: AppConstants.successColor,
          ),
        );
        
        setState(() => _isEditing = false);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البيانات: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تفاصيل ${_getRoleName(widget.user.role)}'),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => setState(() => _isEditing = true),
            ),
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _isLoading ? null : _saveUser,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(AppConstants.paddingMedium),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildUserHeader(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildBasicInfoSection(),
                    const SizedBox(height: AppConstants.paddingLarge),
                    
                    // أقسام خاصة بكل نوع مستخدم
                    if (widget.user.role == UserRole.student)
                      _buildStudentSection(),
                    if (widget.user.role == UserRole.teacher)
                      _buildTeacherSection(),
                    if (widget.user.role == UserRole.parent)
                      _buildParentSection(),
                      
                    const SizedBox(height: AppConstants.paddingLarge),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
    );
  }
  
  Widget _buildUserHeader() {
    return Center(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: _getRoleColor(widget.user.role),
            child: Icon(
              _getRoleIcon(widget.user.role),
              size: 50,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            widget.user.name,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConstants.paddingMedium,
              vertical: AppConstants.paddingSmall,
            ),
            decoration: BoxDecoration(
              color: _getRoleColor(widget.user.role).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            ),
            child: Text(
              _getRoleName(widget.user.role),
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: _getRoleColor(widget.user.role),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildBasicInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المعلومات الأساسية',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // الاسم
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال الاسم';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // البريد الإلكتروني
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال البريد الإلكتروني';
                }
                if (!value.contains('@')) {
                  return 'يرجى إدخال بريد إلكتروني صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // رقم الهاتف
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              enabled: _isEditing,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // كلمة المرور
            TextFormField(
              controller: _passwordController,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                border: const OutlineInputBorder(),
                prefixIcon: const Icon(Icons.lock),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showPassword = !_showPassword;
                    });
                  },
                ),
              ),
              obscureText: !_showPassword,
              enabled: _isEditing,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال كلمة المرور';
                }
                if (value.length < 6) {
                  return 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStudentSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الطالب',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // القسم
            DropdownButtonFormField<String>(
              value: _selectedDepartment,
              decoration: const InputDecoration(
                labelText: 'القسم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.school),
              ),
              items: const [
                DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
                DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
              ],
              onChanged: _isEditing
                  ? (value) {
                      setState(() {
                        _selectedDepartment = value;
                      });
                    }
                  : null,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // الصف
            DropdownButtonFormField<String>(
              value: _selectedGrade,
              decoration: const InputDecoration(
                labelText: 'الصف',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.class_),
              ),
              items: const [
                DropdownMenuItem(value: 'أول', child: Text('أول')),
                DropdownMenuItem(value: 'ثاني', child: Text('ثاني')),
                DropdownMenuItem(value: 'ثالث', child: Text('ثالث')),
              ],
              onChanged: _isEditing
                  ? (value) {
                      setState(() {
                        _selectedGrade = value;
                      });
                    }
                  : null,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // الفصل
            DropdownButtonFormField<String>(
              value: _selectedSection,
              decoration: const InputDecoration(
                labelText: 'الفصل',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.group),
              ),
              items: const [
                DropdownMenuItem(value: 'أ', child: Text('أ')),
                DropdownMenuItem(value: 'ب', child: Text('ب')),
                DropdownMenuItem(value: 'ج', child: Text('ج')),
              ],
              onChanged: _isEditing
                  ? (value) {
                      setState(() {
                        _selectedSection = value;
                      });
                    }
                  : null,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // رقم الطالب
            GestureDetector(
              onTap: () {
                if (_studentId != null) {
                  Clipboard.setData(ClipboardData(text: _studentId!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم نسخ رقم الطالب إلى الحافظة'),
                      backgroundColor: AppConstants.successColor,
                      duration: Duration(seconds: 1),
                    ),
                  );
                }
              },
              child: Container(
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'رقم الطالب:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _studentId ?? 'غير متوفر',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeLarge,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ),
                        const Icon(
                          Icons.copy,
                          size: 18,
                          color: AppConstants.primaryColor,
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    const Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 14,
                          color: AppConstants.textSecondaryColor,
                        ),
                        SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            'اضغط على الرقم لنسخه. يستخدم هذا الرقم لربط الطالب بولي الأمر',
                            style: TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: AppConstants.textSecondaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildTeacherSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات المعلم',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // القسم
            DropdownButtonFormField<String>(
              value: _selectedTeacherDepartment,
              decoration: const InputDecoration(
                labelText: 'القسم',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.school),
              ),
              items: const [
                DropdownMenuItem(value: 'متوسط', child: Text('متوسط')),
                DropdownMenuItem(value: 'ثانوي', child: Text('ثانوي')),
                DropdownMenuItem(value: 'كلاهما', child: Text('كلاهما')),
              ],
              onChanged: _isEditing
                  ? (value) {
                      setState(() {
                        _selectedTeacherDepartment = value;
                      });
                    }
                  : null,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // المواد الدراسية
            const Text(
              'المواد الدراسية:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Wrap(
              spacing: AppConstants.paddingSmall,
              children: [
                for (final subject in _selectedSubjects)
                  Chip(
                    label: Text(subject),
                    backgroundColor: AppConstants.successColor.withOpacity(0.1),
                    deleteIcon: _isEditing ? const Icon(Icons.close, size: 18) : null,
                    onDeleted: _isEditing
                        ? () {
                            setState(() {
                              _selectedSubjects.remove(subject);
                            });
                          }
                        : null,
                  ),
                if (_isEditing)
                  ActionChip(
                    label: const Text('إضافة مادة'),
                    avatar: const Icon(Icons.add, size: 18),
                    onPressed: () {
                      _showAddSubjectDialog();
                    },
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildParentSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات ولي الأمر',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            
            // الطلاب المرتبطين
            const Text(
              'الطلاب المرتبطين:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            
            if (_linkedStudentIds.isEmpty)
              const Text(
                'لا يوجد طلاب مرتبطين',
                style: TextStyle(
                  color: AppConstants.textSecondaryColor,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _linkedStudentIds.length,
                itemBuilder: (context, index) {
                  final studentId = _linkedStudentIds[index];
                  return ListTile(
                    leading: const CircleAvatar(
                      backgroundColor: AppConstants.primaryColor,
                      child: Icon(Icons.person, color: Colors.white),
                    ),
                    title: Text('رقم الطالب: $studentId'),
                    subtitle: const Text('اضغط للاطلاع على تفاصيل الطالب'),
                    trailing: _isEditing
                        ? IconButton(
                            icon: const Icon(Icons.delete, color: AppConstants.errorColor),
                            onPressed: () {
                              setState(() {
                                _linkedStudentIds.removeAt(index);
                              });
                            },
                          )
                        : null,
                    onTap: () {
                      // في تطبيق حقيقي، ستقوم بالانتقال إلى صفحة تفاصيل الطالب
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('سيتم الانتقال إلى صفحة تفاصيل الطالب'),
                          backgroundColor: AppConstants.infoColor,
                        ),
                      );
                    },
                  );
                },
              ),
              
            if (_isEditing) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    _showAddStudentDialog();
                  },
                  icon: const Icon(Icons.add),
                  label: const Text('ربط طالب جديد'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isEditing ? () {
              setState(() {
                _isEditing = false;
                
                // إعادة تعيين البيانات إلى القيم الأصلية
                _nameController.text = widget.user.name;
                _emailController.text = widget.user.email;
                _phoneController.text = widget.user.phoneNumber ?? '';
                _passwordController.text = widget.user.password;
                
                _initUserSpecificData();
              });
            } : () {
              Navigator.pop(context);
            },
            icon: Icon(_isEditing ? Icons.cancel : Icons.arrow_back),
            label: Text(_isEditing ? 'إلغاء التعديل' : 'رجوع'),
            style: ElevatedButton.styleFrom(
              backgroundColor: _isEditing ? AppConstants.errorColor : Colors.grey,
            ),
          ),
        ),
        if (_isEditing) ...[
          const SizedBox(width: AppConstants.paddingMedium),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _saveUser,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.save),
              label: const Text('حفظ التغييرات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.successColor,
              ),
            ),
          ),
        ],
      ],
    );
  }
  
  void _showAddSubjectDialog() {
    final TextEditingController subjectController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إضافة مادة دراسية'),
        content: TextField(
          controller: subjectController,
          decoration: const InputDecoration(
            labelText: 'اسم المادة',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (subjectController.text.isNotEmpty) {
                setState(() {
                  _selectedSubjects.add(subjectController.text);
                });
                Navigator.pop(context);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    ).then((_) => subjectController.dispose());
  }
  
  void _showAddStudentDialog() {
    final TextEditingController studentIdController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('ربط طالب جديد'),
        content: TextField(
          controller: studentIdController,
          decoration: const InputDecoration(
            labelText: 'رقم الطالب (ID)',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (studentIdController.text.isNotEmpty) {
                setState(() {
                  _linkedStudentIds.add(studentIdController.text);
                });
                Navigator.pop(context);
              }
            },
            child: const Text('ربط'),
          ),
        ],
      ),
    ).then((_) => studentIdController.dispose());
  }
  
  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return AppConstants.errorColor;
      case UserRole.teacher:
        return AppConstants.successColor;
      case UserRole.student:
        return AppConstants.primaryColor;
      case UserRole.parent:
        return AppConstants.warningColor;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Icons.admin_panel_settings;
      case UserRole.teacher:
        return Icons.school;
      case UserRole.student:
        return Icons.person;
      case UserRole.parent:
        return Icons.family_restroom;
    }
  }

  String _getRoleName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'المدير';
      case UserRole.teacher:
        return 'المعلم';
      case UserRole.student:
        return 'الطالب';
      case UserRole.parent:
        return 'ولي الأمر';
    }
  }
}
