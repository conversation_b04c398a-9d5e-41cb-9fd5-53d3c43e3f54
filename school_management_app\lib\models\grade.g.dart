// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'grade.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class GradeAdapter extends TypeAdapter<Grade> {
  @override
  final int typeId = 6;

  @override
  Grade read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Grade(
      id: fields[0] as String,
      studentId: fields[1] as String,
      teacherId: fields[2] as String,
      subject: fields[3] as String,
      assignmentId: fields[4] as String?,
      score: (fields[5] as num).toDouble(),
      maxScore: (fields[6] as num).toDouble(),
      comments: fields[7] as String?,
      gradedAt: fields[8] as DateTime,
      gradeType: fields[9] as String,
      semester: fields[10] as String,
    );
  }

  @override
  void write(BinaryWriter writer, Grade obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.studentId)
      ..writeByte(2)
      ..write(obj.teacherId)
      ..writeByte(3)
      ..write(obj.subject)
      ..writeByte(4)
      ..write(obj.assignmentId)
      ..writeByte(5)
      ..write(obj.score)
      ..writeByte(6)
      ..write(obj.maxScore)
      ..writeByte(7)
      ..write(obj.comments)
      ..writeByte(8)
      ..write(obj.gradedAt)
      ..writeByte(9)
      ..write(obj.gradeType)
      ..writeByte(10)
      ..write(obj.semester);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GradeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
