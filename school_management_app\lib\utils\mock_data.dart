import 'package:uuid/uuid.dart';
import '../models/user.dart';
import '../models/student.dart';
import '../models/teacher.dart';

class MockData {
  static const _uuid = Uuid();

  static List<User> getMockUsers() {
    final now = DateTime.now();

    return [
      // Admin
      User(
        id: 'admin_1',
        name: 'أحمد محمد',
        email: '<EMAIL>',
        password: 'admin123',
        role: UserRole.admin,
        phoneNumber: '+************',
        createdAt: now.subtract(const Duration(days: 365)),
        updatedAt: now,
      ),

      // Teachers
      User(
        id: 'teacher_1',
        name: 'فاطمة علي',
        email: '<EMAIL>',
        password: 'teacher123',
        role: UserRole.teacher,
        phoneNumber: '+************',
        createdAt: now.subtract(const Duration(days: 200)),
        updatedAt: now,
      ),
      User(
        id: 'teacher_2',
        name: 'محمد سالم',
        email: '<EMAIL>',
        password: 'teacher123',
        role: UserRole.teacher,
        phoneNumber: '+************',
        createdAt: now.subtract(const Duration(days: 180)),
        updatedAt: now,
      ),

      // Students
      User(
        id: 'student_1',
        name: 'سارة أحمد',
        email: '<EMAIL>',
        password: 'student123',
        role: UserRole.student,
        phoneNumber: '+966504567890',
        createdAt: now.subtract(const Duration(days: 100)),
        updatedAt: now,
      ),
      User(
        id: 'student_2',
        name: 'عبدالله محمد',
        email: '<EMAIL>',
        password: 'student123',
        role: UserRole.student,
        phoneNumber: '+966505678901',
        createdAt: now.subtract(const Duration(days: 95)),
        updatedAt: now,
      ),
      User(
        id: 'student_3',
        name: 'نورا سالم',
        email: '<EMAIL>',
        password: 'student123',
        role: UserRole.student,
        phoneNumber: '+966506789012',
        createdAt: now.subtract(const Duration(days: 90)),
        updatedAt: now,
      ),

      // Parents
      User(
        id: 'parent_1',
        name: 'أحمد عبدالله',
        email: '<EMAIL>',
        password: 'parent123',
        role: UserRole.parent,
        phoneNumber: '+966507890123',
        createdAt: now.subtract(const Duration(days: 120)),
        updatedAt: now,
      ),
      User(
        id: 'parent_2',
        name: 'مريم محمد',
        email: '<EMAIL>',
        password: 'parent123',
        role: UserRole.parent,
        phoneNumber: '+966508901234',
        createdAt: now.subtract(const Duration(days: 115)),
        updatedAt: now,
      ),
    ];
  }

  static List<Student> getMockStudents() {
    final now = DateTime.now();

    return [
      Student(
        id: 'student_profile_1',
        userId: 'student_1',
        studentNumber: 'STU001',
        className: 'الصف العاشر أ',
        gradeLevel: 10,
        section: 'أ',
        enrollmentDate: now.subtract(const Duration(days: 100)),
        parentIds: ['parent_1'],
        address: 'الرياض، المملكة العربية السعودية',
        dateOfBirth: DateTime(2008, 5, 15),
        emergencyContact: '+966507890123',
      ),
      Student(
        id: 'student_profile_2',
        userId: 'student_2',
        studentNumber: 'STU002',
        className: 'الصف العاشر أ',
        gradeLevel: 10,
        section: 'أ',
        enrollmentDate: now.subtract(const Duration(days: 95)),
        parentIds: ['parent_2'],
        address: 'جدة، المملكة العربية السعودية',
        dateOfBirth: DateTime(2008, 8, 22),
        emergencyContact: '+966508901234',
      ),
      Student(
        id: 'student_profile_3',
        userId: 'student_3',
        studentNumber: 'STU003',
        className: 'الصف التاسع ب',
        gradeLevel: 9,
        section: 'ب',
        enrollmentDate: now.subtract(const Duration(days: 90)),
        parentIds: ['parent_1'],
        address: 'الدمام، المملكة العربية السعودية',
        dateOfBirth: DateTime(2009, 3, 10),
        emergencyContact: '+966507890123',
      ),
    ];
  }

  static List<Teacher> getMockTeachers() {
    final now = DateTime.now();

    return [
      Teacher(
        id: 'teacher_profile_1',
        userId: 'teacher_1',
        employeeNumber: 'TCH001',
        subjects: ['الرياضيات', 'الفيزياء'],
        classes: ['الصف العاشر أ', 'الصف التاسع ب'],
        department: 'العلوم',
        hireDate: now.subtract(const Duration(days: 200)),
        qualification: 'بكالوريوس رياضيات',
        experienceYears: 5,
        specialization: 'الرياضيات التطبيقية',
        isClassTeacher: true,
        classTeacherOf: 'الصف العاشر أ',
      ),
      Teacher(
        id: 'teacher_profile_2',
        userId: 'teacher_2',
        employeeNumber: 'TCH002',
        subjects: ['اللغة العربية', 'التاريخ'],
        classes: ['الصف العاشر أ', 'الصف التاسع ب'],
        department: 'الأدب',
        hireDate: now.subtract(const Duration(days: 180)),
        qualification: 'ماجستير أدب عربي',
        experienceYears: 8,
        specialization: 'الأدب الحديث',
        isClassTeacher: false,
      ),
    ];
  }
}
