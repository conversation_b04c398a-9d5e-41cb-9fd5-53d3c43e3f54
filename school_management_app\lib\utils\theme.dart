import 'package:flutter/material.dart';

/// ملف توحيد الهوية البصرية للتطبيق
/// يحتوي على جميع الألوان والخطوط والأنماط المستخدمة في التطبيق
class AppTheme {
  AppTheme._(); // منع إنشاء نسخة من الكلاس

  // ألوان التطبيق الرئيسية
  static const Color primaryColor = Color(0xFF1A5CFF); // اللون الأزرق الرئيسي
  static const Color secondaryColor = Color(0xFFFFD166); // اللون الأصفر الثانوي
  static const Color accentColor = Color(0xFFFF6B6B); // اللون الأحمر للتأكيد

  // ألوان محايدة
  static const Color backgroundColor = Color(0xFFF5F5F5); // خلفية فاتحة
  static const Color surfaceColor = Colors.white; // لون السطح
  static const Color textPrimaryColor = Color(0xFF333333); // نص داكن
  static const Color textSecondaryColor = Color(0xFF666666); // نص متوسط
  static const Color dividerColor = Color(0xFFEEEEEE); // لون الفواصل

  // ألوان الحالة
  static const Color successColor = Color(0xFF4CAF50); // نجاح
  static const Color warningColor = Color(0xFFFFC107); // تحذير
  static const Color errorColor = Color(0xFFF44336); // خطأ
  static const Color infoColor = Color(0xFF2196F3); // معلومات

  // نصف قطر الحواف
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 24.0;
  static const double borderRadiusCircular = 100.0;

  // المسافات
  static const double spacingXSmall = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingMedium = 16.0;
  static const double spacingLarge = 24.0;
  static const double spacingXLarge = 32.0;
  static const double spacingXXLarge = 48.0;

  // أحجام الخطوط
  static const double fontSizeXSmall = 12.0;
  static const double fontSizeSmall = 14.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeXLarge = 20.0;
  static const double fontSizeXXLarge = 24.0;
  static const double fontSizeDisplay = 34.0;

  // سمك الخطوط
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;

  // الظلال
  static List<BoxShadow> shadowSmall = [
    BoxShadow(
      color: Colors.black.withAlpha(10),
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> shadowMedium = [
    BoxShadow(
      color: Colors.black.withAlpha(15),
      blurRadius: 8,
      offset: const Offset(0, 4),
    ),
  ];

  static List<BoxShadow> shadowLarge = [
    BoxShadow(
      color: Colors.black.withAlpha(20),
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];

  // ثيم التطبيق الرئيسي
  static ThemeData lightTheme = ThemeData(
    // ألوان أساسية
    primaryColor: primaryColor,
    colorScheme: const ColorScheme.light(
      primary: primaryColor,
      secondary: secondaryColor,
      tertiary: accentColor,
      surface: surfaceColor,
      error: errorColor,
    ),
    scaffoldBackgroundColor: backgroundColor,

    // الخطوط
    fontFamily: 'Cairo',
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: fontSizeDisplay,
        fontWeight: fontWeightBold,
        color: textPrimaryColor,
      ),
      displayMedium: TextStyle(
        fontSize: fontSizeXXLarge,
        fontWeight: fontWeightBold,
        color: textPrimaryColor,
      ),
      displaySmall: TextStyle(
        fontSize: fontSizeXLarge,
        fontWeight: fontWeightSemiBold,
        color: textPrimaryColor,
      ),
      headlineMedium: TextStyle(
        fontSize: fontSizeLarge,
        fontWeight: fontWeightSemiBold,
        color: textPrimaryColor,
      ),
      titleLarge: TextStyle(
        fontSize: fontSizeMedium,
        fontWeight: fontWeightMedium,
        color: textPrimaryColor,
      ),
      bodyLarge: TextStyle(
        fontSize: fontSizeMedium,
        fontWeight: fontWeightRegular,
        color: textPrimaryColor,
      ),
      bodyMedium: TextStyle(
        fontSize: fontSizeSmall,
        fontWeight: fontWeightRegular,
        color: textPrimaryColor,
      ),
      labelLarge: TextStyle(
        fontSize: fontSizeSmall,
        fontWeight: fontWeightMedium,
        color: primaryColor,
      ),
    ),

    // الأزرار
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(
          horizontal: spacingLarge,
          vertical: spacingMedium,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
        elevation: 0,
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        padding: const EdgeInsets.symmetric(
          horizontal: spacingMedium,
          vertical: spacingSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: primaryColor,
        side: const BorderSide(color: primaryColor),
        padding: const EdgeInsets.symmetric(
          horizontal: spacingLarge,
          vertical: spacingMedium,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadiusMedium),
        ),
      ),
    ),

    // حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: surfaceColor,
      contentPadding: const EdgeInsets.all(spacingMedium),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: const BorderSide(color: dividerColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: const BorderSide(color: dividerColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: const BorderSide(color: primaryColor),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        borderSide: const BorderSide(color: errorColor),
      ),
      hintStyle: const TextStyle(
        color: textSecondaryColor,
        fontSize: fontSizeSmall,
      ),
    ),

    // البطاقات
    cardTheme: CardThemeData(
      color: surfaceColor,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadiusMedium),
        side: const BorderSide(color: dividerColor),
      ),
    ),

    // شريط التطبيق
    appBarTheme: const AppBarTheme(
      backgroundColor: surfaceColor,
      foregroundColor: textPrimaryColor,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: textPrimaryColor,
        fontSize: fontSizeLarge,
        fontWeight: fontWeightSemiBold,
        fontFamily: 'Cairo',
      ),
    ),

    // القوائم
    listTileTheme: const ListTileThemeData(
      contentPadding: EdgeInsets.symmetric(
        horizontal: spacingMedium,
        vertical: spacingSmall,
      ),
    ),

    // الفواصل
    dividerTheme: const DividerThemeData(
      color: dividerColor,
      thickness: 1,
      space: spacingMedium,
    ),

    // الرسوم المتحركة
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: ZoomPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
  );
}
