import 'package:flutter/material.dart';
import '../utils/constants.dart';

class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  final SlideDirection direction;

  SlidePageRoute({
    required this.page,
    this.direction = SlideDirection.rightToLeft,
  }) : super(
         pageBuilder: (context, animation, secondaryAnimation) => page,
         transitionsBuilder: (context, animation, secondaryAnimation, child) {
           Offset begin;
           switch (direction) {
             case SlideDirection.rightToLeft:
               begin = const Offset(1.0, 0.0);
               break;
             case SlideDirection.leftToRight:
               begin = const Offset(-1.0, 0.0);
               break;
             case SlideDirection.topToBottom:
               begin = const Offset(0.0, -1.0);
               break;
             case SlideDirection.bottomToTop:
               begin = const Offset(0.0, 1.0);
               break;
           }

           return SlideTransition(
             position: Tween<Offset>(begin: begin, end: Offset.zero).animate(
               CurvedAnimation(parent: animation, curve: Curves.easeInOutCubic),
             ),
             child: child,
           );
         },
         transitionDuration: AppConstants.animationDurationMedium,
         reverseTransitionDuration: AppConstants.animationDurationMedium,
       );
}

enum SlideDirection { rightToLeft, leftToRight, topToBottom, bottomToTop }

class FadePageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;

  FadePageRoute({required this.page})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: AppConstants.animationDurationMedium,
      );
}

class ScalePageRoute extends PageRouteBuilder {
  final Widget page;

  ScalePageRoute({required this.page})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return ScaleTransition(
            scale: Tween<double>(begin: 0.8, end: 1.0).animate(
              CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
            ),
            child: FadeTransition(opacity: animation, child: child),
          );
        },
        transitionDuration: AppConstants.animationDurationMedium,
      );
}

class RotationPageRoute extends PageRouteBuilder {
  final Widget page;

  RotationPageRoute({required this.page})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return RotationTransition(
            turns: Tween<double>(begin: 0.8, end: 1.0).animate(
              CurvedAnimation(parent: animation, curve: Curves.easeInOut),
            ),
            child: ScaleTransition(scale: animation, child: child),
          );
        },
        transitionDuration: AppConstants.animationDurationLong,
      );
}

class CustomPageRoute extends PageRouteBuilder {
  final Widget page;
  final Widget Function(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  )
  transitionBuilder;

  CustomPageRoute({required this.page, required this.transitionBuilder})
    : super(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: transitionBuilder,
        transitionDuration: AppConstants.animationDurationMedium,
      );
}

// مساعد للتنقل مع الرسوم المتحركة
class AnimatedNavigator {
  static Future<T?> push<T extends Object?>(
    BuildContext context,
    Widget page, {
    PageTransitionType type = PageTransitionType.slide,
    SlideDirection direction = SlideDirection.rightToLeft,
  }) {
    Route<T> route;

    switch (type) {
      case PageTransitionType.slide:
        route = SlidePageRoute<T>(page: page, direction: direction);
        break;
      case PageTransitionType.fade:
        route = FadePageRoute<T>(page: page);
        break;
      case PageTransitionType.scale:
        route = ScalePageRoute<T>(page: page);
        break;
      case PageTransitionType.rotation:
        route = RotationPageRoute<T>(page: page);
        break;
    }

    return Navigator.of(context).push<T>(route);
  }

  static Future<T?> pushReplacement<T extends Object?, TO extends Object?>(
    BuildContext context,
    Widget page, {
    PageTransitionType type = PageTransitionType.slide,
    SlideDirection direction = SlideDirection.rightToLeft,
    TO? result,
  }) {
    PageRouteBuilder route;

    switch (type) {
      case PageTransitionType.slide:
        route = SlidePageRoute(page: page, direction: direction);
        break;
      case PageTransitionType.fade:
        route = FadePageRoute(page: page);
        break;
      case PageTransitionType.scale:
        route = ScalePageRoute(page: page);
        break;
      case PageTransitionType.rotation:
        route = RotationPageRoute(page: page);
        break;
    }

    return Navigator.of(context).pushReplacement<T, TO>(route, result: result);
  }

  static Future<T?> pushAndRemoveUntil<T extends Object?>(
    BuildContext context,
    Widget page,
    RoutePredicate predicate, {
    PageTransitionType type = PageTransitionType.slide,
    SlideDirection direction = SlideDirection.rightToLeft,
  }) {
    PageRouteBuilder route;

    switch (type) {
      case PageTransitionType.slide:
        route = SlidePageRoute(page: page, direction: direction);
        break;
      case PageTransitionType.fade:
        route = FadePageRoute(page: page);
        break;
      case PageTransitionType.scale:
        route = ScalePageRoute(page: page);
        break;
      case PageTransitionType.rotation:
        route = RotationPageRoute(page: page);
        break;
    }

    return Navigator.of(context).pushAndRemoveUntil<T>(route, predicate);
  }
}

enum PageTransitionType { slide, fade, scale, rotation }

// مكون للرسوم المتحركة عند الظهور
class AnimatedAppearance extends StatefulWidget {
  final Widget child;
  final Duration delay;
  final Duration duration;
  final Curve curve;
  final AnimationType type;

  const AnimatedAppearance({
    super.key,
    required this.child,
    this.delay = Duration.zero,
    this.duration = const Duration(milliseconds: 500),
    this.curve = Curves.easeOutBack,
    this.type = AnimationType.slideUp,
  });

  @override
  State<AnimatedAppearance> createState() => _AnimatedAppearanceState();
}

class _AnimatedAppearanceState extends State<AnimatedAppearance>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this);

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    _slideAnimation = Tween<Offset>(
      begin: _getBeginOffset(),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  Offset _getBeginOffset() {
    switch (widget.type) {
      case AnimationType.slideUp:
        return const Offset(0, 0.3);
      case AnimationType.slideDown:
        return const Offset(0, -0.3);
      case AnimationType.slideLeft:
        return const Offset(0.3, 0);
      case AnimationType.slideRight:
        return const Offset(-0.3, 0);
      case AnimationType.fade:
      case AnimationType.scale:
        return Offset.zero;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.type) {
      case AnimationType.fade:
        return FadeTransition(opacity: _animation, child: widget.child);
      case AnimationType.scale:
        return ScaleTransition(scale: _animation, child: widget.child);
      case AnimationType.slideUp:
      case AnimationType.slideDown:
      case AnimationType.slideLeft:
      case AnimationType.slideRight:
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(opacity: _animation, child: widget.child),
        );
    }
  }
}

enum AnimationType { fade, scale, slideUp, slideDown, slideLeft, slideRight }

// مكون للرسوم المتحركة المتتالية
class StaggeredAnimationList extends StatefulWidget {
  final List<Widget> children;
  final Duration staggerDelay;
  final Duration itemDuration;
  final AnimationType animationType;

  const StaggeredAnimationList({
    super.key,
    required this.children,
    this.staggerDelay = const Duration(milliseconds: 100),
    this.itemDuration = const Duration(milliseconds: 500),
    this.animationType = AnimationType.slideUp,
  });

  @override
  State<StaggeredAnimationList> createState() => _StaggeredAnimationListState();
}

class _StaggeredAnimationListState extends State<StaggeredAnimationList> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: widget.children.asMap().entries.map((entry) {
        int index = entry.key;
        Widget child = entry.value;

        return AnimatedAppearance(
          delay: widget.staggerDelay * index,
          duration: widget.itemDuration,
          type: widget.animationType,
          child: child,
        );
      }).toList(),
    );
  }
}
