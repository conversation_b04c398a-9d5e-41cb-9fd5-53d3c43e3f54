import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/dashboard_card.dart';
import '../../widgets/modern_app_bar.dart';
import '../../widgets/modern_widgets.dart';
import '../../widgets/animated_widgets.dart' as animated;
import '../../widgets/responsive_layout.dart';
import 'user_management_screen.dart';
import 'schedule_management_screen.dart';
import 'reports_screen.dart';
import 'admin_settings_screen.dart';
import '../auth/login_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _selectedIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      AdminHomeScreen(
        onTabChange: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
      ),
      const UserManagementScreen(),
      const ScheduleManagementScreen(),
      const AdminSettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ModernAppBar(
        title: 'لوحة تحكم المدير',
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
          ),
        ],
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'إدارة المستخدمين',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.schedule),
            label: 'إدارة الجداول',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }

  Future<void> _logout(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }
}

class AdminHomeScreen extends StatelessWidget {
  final Function(int)? onTabChange;

  const AdminHomeScreen({super.key, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Background with gradient
        Container(
          decoration: const BoxDecoration(
            gradient: AppConstants.primaryGradient,
          ),
        ),

        // Content
        ResponsiveContainer(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: StaggeredAnimationList(
              children: [
                // Welcome Section
                Consumer<AuthProvider>(
                  builder: (context, authProvider, child) {
                    return ModernCard(
                      padding: const EdgeInsets.all(AppConstants.paddingLarge),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(
                              AppConstants.paddingMedium,
                            ),
                            decoration: BoxDecoration(
                              gradient: AppConstants.primaryGradient,
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: AppConstants.primaryColor.withValues(
                                    alpha: 0.3,
                                  ),
                                  blurRadius: 10,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.person,
                              size: AppConstants.iconSizeLarge,
                              color: AppConstants.textOnPrimaryColor,
                            ),
                          ),
                          const SizedBox(width: AppConstants.paddingLarge),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                animated.TypewriterText(
                                  text:
                                      'مرحباً، ${authProvider.currentUser?.name ?? 'المدير'}',
                                  style: const TextStyle(
                                    fontSize: AppConstants.fontSizeXLarge,
                                    fontWeight: AppConstants.fontWeightBold,
                                    color: AppConstants.textPrimaryColor,
                                  ),
                                ),
                                const SizedBox(
                                  height: AppConstants.paddingSmall,
                                ),
                                const Text(
                                  'لوحة تحكم المدير',
                                  style: TextStyle(
                                    fontSize: AppConstants.fontSizeLarge,
                                    fontWeight: AppConstants.fontWeightMedium,
                                    color: AppConstants.textSecondaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Statistics Cards
                animated.SlideInWidget(
                  direction: animated.SlideDirection.left,
                  child: const Text(
                    'إحصائيات عامة',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeXLarge,
                      fontWeight: AppConstants.fontWeightBold,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),

                ResponsiveGrid(
                  children: [
                    DashboardCard(
                      title: 'إجمالي الطلاب',
                      value: '150',
                      icon: Icons.school,
                      color: AppConstants.primaryColor,
                      showProgress: true,
                      progressValue: 0.75,
                      subtitle: 'نمو 15% هذا الشهر',
                      onTap: () {
                        // Navigate to students list
                      },
                    ),
                    DashboardCard(
                      title: 'إجمالي المعلمين',
                      value: '25',
                      icon: Icons.person,
                      color: AppConstants.successColor,
                      showProgress: true,
                      progressValue: 0.85,
                      subtitle: 'معلم نشط',
                      trailing: const Icon(
                        Icons.trending_up,
                        color: AppConstants.successColor,
                      ),
                      onTap: () {
                        // Navigate to teachers list
                      },
                    ),
                    DashboardCard(
                      title: 'إجمالي أولياء الأمور',
                      value: '120',
                      icon: Icons.family_restroom,
                      color: AppConstants.warningColor,
                      showProgress: true,
                      progressValue: 0.60,
                      subtitle: 'مسجل في النظام',
                      onTap: () {
                        // Navigate to parents list
                      },
                    ),
                    DashboardCard(
                      title: 'الصفوف الدراسية',
                      value: '12',
                      icon: Icons.class_,
                      color: AppConstants.infoColor,
                      showProgress: true,
                      progressValue: 1.0,
                      subtitle: 'صف دراسي',
                      onTap: () {
                        // Navigate to classes list
                      },
                    ),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Quick Actions
                animated.SlideInWidget(
                  direction: animated.SlideDirection.right,
                  child: const Text(
                    'إجراءات سريعة',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeXLarge,
                      fontWeight: AppConstants.fontWeightBold,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingMedium),

                ResponsiveGrid(
                  children: [
                    _buildQuickActionCard(
                      context,
                      'إضافة مستخدم جديد',
                      Icons.person_add,
                      AppConstants.primaryColor,
                      () {
                        _showAddUserDialog(context);
                      },
                    ),
                    _buildQuickActionCard(
                      context,
                      'رفع جدول دراسي',
                      Icons.upload_file,
                      AppConstants.successColor,
                      () {
                        _navigateToScheduleManagement(context);
                      },
                    ),
                    _buildQuickActionCard(
                      context,
                      'إدارة المستخدمين',
                      Icons.people,
                      AppConstants.warningColor,
                      () {
                        _navigateToUserManagement(context);
                      },
                    ),
                    _buildQuickActionCard(
                      context,
                      'التقارير',
                      Icons.analytics,
                      AppConstants.infoColor,
                      () {
                        _showReportsDialog(context);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 40, color: color),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Navigation methods for quick actions
  void _navigateToScheduleManagement(BuildContext context) {
    onTabChange?.call(2); // Schedule management tab
  }

  void _navigateToUserManagement(BuildContext context) {
    onTabChange?.call(1); // User management tab
  }

  void _showAddUserDialog(BuildContext context) {
    String? selectedUserType;
    String? selectedDepartment;
    String? selectedGrade;
    String? selectedSection;
    String? studentLinkId; // ID entered by parent to link to student
    String generatedId = ''; // ID generated for new student

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Generate random ID for student
            if (selectedUserType == 'student' &&
                selectedDepartment != null &&
                selectedGrade != null &&
                selectedSection != null &&
                generatedId.isEmpty) {
              // Generate ID based on department, grade and section
              String deptPrefix = selectedDepartment == 'متوسط' ? 'M' : 'H';
              String gradePrefix = selectedGrade == 'أول'
                  ? '1'
                  : (selectedGrade == 'ثاني' ? '2' : '3');
              String sectionPrefix = selectedSection == 'أ'
                  ? 'A'
                  : (selectedSection == 'ب' ? 'B' : 'C');
              // Random 4-digit number
              String randomNum =
                  (1000 + DateTime.now().millisecondsSinceEpoch % 9000)
                      .toString();
              generatedId = '$deptPrefix$gradePrefix$sectionPrefix$randomNum';

              setState(() {});
            }

            return AlertDialog(
              title: const Text('إضافة مستخدم جديد'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Basic fields for all user types
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'الاسم',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),

                    // User Type Dropdown
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'نوع المستخدم',
                        border: OutlineInputBorder(),
                      ),
                      value: selectedUserType,
                      items: const [
                        DropdownMenuItem(value: 'admin', child: Text('مدير')),
                        DropdownMenuItem(value: 'teacher', child: Text('معلم')),
                        DropdownMenuItem(value: 'student', child: Text('طالب')),
                        DropdownMenuItem(
                          value: 'parent',
                          child: Text('ولي أمر'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedUserType = value;
                          // Reset other selections when user type changes
                          selectedDepartment = null;
                          selectedGrade = null;
                          selectedSection = null;
                          studentLinkId = null;
                          generatedId = '';
                        });
                      },
                    ),

                    // Conditional fields based on user type
                    if (selectedUserType == 'teacher') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedDepartment,
                        items: const [
                          DropdownMenuItem(
                            value: 'متوسط',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'ثانوي',
                            child: Text('ثانوي'),
                          ),
                          DropdownMenuItem(
                            value: 'كلاهما',
                            child: Text('كلاهما'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedDepartment = value;
                          });
                        },
                      ),
                    ],

                    if (selectedUserType == 'student') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedDepartment,
                        items: const [
                          DropdownMenuItem(
                            value: 'متوسط',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'ثانوي',
                            child: Text('ثانوي'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedDepartment = value;
                            generatedId = '';
                          });
                        },
                      ),

                      if (selectedDepartment != null) ...[
                        const SizedBox(height: AppConstants.paddingMedium),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الصف',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedGrade,
                          items: const [
                            DropdownMenuItem(value: 'أول', child: Text('أول')),
                            DropdownMenuItem(
                              value: 'ثاني',
                              child: Text('ثاني'),
                            ),
                            DropdownMenuItem(
                              value: 'ثالث',
                              child: Text('ثالث'),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedGrade = value;
                              generatedId = '';
                            });
                          },
                        ),

                        const SizedBox(height: AppConstants.paddingMedium),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الفصل',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedSection,
                          items: const [
                            DropdownMenuItem(value: 'أ', child: Text('أ')),
                            DropdownMenuItem(value: 'ب', child: Text('ب')),
                            DropdownMenuItem(value: 'ج', child: Text('ج')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedSection = value;
                              generatedId = '';
                            });
                          },
                        ),
                      ],

                      if (generatedId.isNotEmpty) ...[
                        const SizedBox(height: AppConstants.paddingMedium),
                        Container(
                          padding: const EdgeInsets.all(
                            AppConstants.paddingMedium,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(
                              AppConstants.radiusMedium,
                            ),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'رقم الطالب:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.textSecondaryColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              GestureDetector(
                                onTap: () {
                                  // Copy to clipboard when tapped
                                  Clipboard.setData(
                                    ClipboardData(text: generatedId),
                                  );
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'تم نسخ رقم الطالب إلى الحافظة',
                                      ),
                                      backgroundColor:
                                          AppConstants.successColor,
                                      duration: Duration(seconds: 1),
                                    ),
                                  );
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                    horizontal: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.blue.shade300,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        generatedId,
                                        style: const TextStyle(
                                          fontSize: AppConstants.fontSizeLarge,
                                          fontWeight: FontWeight.bold,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(
                                        Icons.copy,
                                        size: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    size: 14,
                                    color: AppConstants.textSecondaryColor,
                                  ),
                                  SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      'اضغط على الرقم لنسخه. سيتم استخدام هذا الرقم لربط الطالب بولي الأمر',
                                      style: TextStyle(
                                        fontSize: AppConstants.fontSizeSmall,
                                        color: AppConstants.textSecondaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],

                    if (selectedUserType == 'parent') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      TextField(
                        decoration: const InputDecoration(
                          labelText: 'رقم الطالب (ID)',
                          hintText: 'أدخل رقم الطالب المراد متابعته',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          setState(() {
                            studentLinkId = value;
                          });
                        },
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إضافة المستخدم بنجاح'),
                        backgroundColor: AppConstants.successColor,
                      ),
                    );
                  },
                  child: const Text('إضافة'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showReportsDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReportsScreen()),
    );
  }
}
