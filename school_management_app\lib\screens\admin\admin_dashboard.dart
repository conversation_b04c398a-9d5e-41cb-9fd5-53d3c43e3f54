import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/modern_app_bar.dart';
import 'user_management_screen.dart';
import 'schedule_management_screen.dart';
import 'reports_screen.dart';
import 'admin_settings_screen.dart';
import '../auth/login_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _selectedIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      AdminHomeScreen(
        onTabChange: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
      ),
      const UserManagementScreen(),
      const ScheduleManagementScreen(),
      const AdminSettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ModernAppBar(
        title: 'لوحة تحكم المدير',
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
          ),
        ],
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'إدارة المستخدمين',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.schedule),
            label: 'إدارة الجداول',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }

  Future<void> _logout(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }
}

class AdminHomeScreen extends StatelessWidget {
  final Function(int)? onTabChange;

  const AdminHomeScreen({super.key, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F7FA),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              Container(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingLarge,
                ),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppConstants.primaryColor,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppConstants.primaryColor.withValues(
                              alpha: 0.3,
                            ),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.admin_panel_settings,
                        size: 32,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مرحباً بك، المدير',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2D3748),
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'لوحة التحكم الرئيسية',
                            style: TextStyle(
                              fontSize: 16,
                              color: Color(0xFF718096),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(
                                0xFF48BB78,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.circle,
                                  size: 8,
                                  color: Color(0xFF48BB78),
                                ),
                                SizedBox(width: 6),
                                Text(
                                  'نشط الآن',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF48BB78),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7FAFC),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.notifications_outlined,
                        color: Color(0xFF4A5568),
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ),

              // Statistics Section
              Container(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingMedium,
                ),
                child: const Row(
                  children: [
                    Text(
                      'إحصائيات المدرسة',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                  ],
                ),
              ),

              // Statistics Cards
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.3,
                children: [
                  _buildCleanStatCard(
                    'إجمالي الطلاب',
                    '150',
                    Icons.school_outlined,
                    const Color(0xFF667eea),
                    '15%',
                    true,
                  ),
                  _buildCleanStatCard(
                    'المعلمين',
                    '25',
                    Icons.person_outline,
                    const Color(0xFF48BB78),
                    '8%',
                    true,
                  ),
                  _buildCleanStatCard(
                    'أولياء الأمور',
                    '120',
                    Icons.family_restroom_outlined,
                    const Color(0xFFED8936),
                    '12%',
                    false,
                  ),
                  _buildCleanStatCard(
                    'الصفوف',
                    '12',
                    Icons.class_outlined,
                    const Color(0xFF9F7AEA),
                    '100%',
                    true,
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Quick Actions Section
              Container(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingMedium,
                ),
                child: const Row(
                  children: [
                    Text(
                      'الإجراءات السريعة',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D3748),
                      ),
                    ),
                  ],
                ),
              ),

              // Quick Actions
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.4,
                children: [
                  _buildCleanActionCard(
                    context,
                    'إضافة مستخدم',
                    'إضافة مستخدم جديد للنظام',
                    Icons.person_add_outlined,
                    const Color(0xFF667eea),
                    () => _showAddUserDialog(context),
                  ),
                  _buildCleanActionCard(
                    context,
                    'الجداول الدراسية',
                    'رفع وإدارة الجداول',
                    Icons.calendar_today_outlined,
                    const Color(0xFF48BB78),
                    () => _navigateToScheduleManagement(context),
                  ),
                  _buildCleanActionCard(
                    context,
                    'إدارة المستخدمين',
                    'عرض وتعديل المستخدمين',
                    Icons.people_outline,
                    const Color(0xFFED8936),
                    () => _navigateToUserManagement(context),
                  ),
                  _buildCleanActionCard(
                    context,
                    'التقارير',
                    'عرض التقارير والإحصائيات',
                    Icons.analytics_outlined,
                    const Color(0xFF9F7AEA),
                    () => _showReportsDialog(context),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCleanStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String percentage,
    bool isPositive,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isPositive
                      ? const Color(0xFF48BB78).withValues(alpha: 0.1)
                      : const Color(0xFFE53E3E).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPositive ? Icons.trending_up : Icons.trending_down,
                      size: 12,
                      color: isPositive
                          ? const Color(0xFF48BB78)
                          : const Color(0xFFE53E3E),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      percentage,
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: FontWeight.w600,
                        color: isPositive
                            ? const Color(0xFF48BB78)
                            : const Color(0xFFE53E3E),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF718096),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCleanActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D3748),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF718096),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Navigation methods for quick actions
  void _navigateToScheduleManagement(BuildContext context) {
    onTabChange?.call(2); // Schedule management tab
  }

  void _navigateToUserManagement(BuildContext context) {
    onTabChange?.call(1); // User management tab
  }

  void _showAddUserDialog(BuildContext context) {
    String? selectedUserType;
    String? selectedDepartment;
    String? selectedGrade;
    String? selectedSection;
    String generatedId = ''; // ID generated for new student

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Generate random ID for student
            if (selectedUserType == 'student' &&
                selectedDepartment != null &&
                selectedGrade != null &&
                selectedSection != null &&
                generatedId.isEmpty) {
              // Generate ID based on department, grade and section
              String deptPrefix = selectedDepartment == 'متوسط' ? 'M' : 'H';
              String gradePrefix = selectedGrade == 'أول'
                  ? '1'
                  : (selectedGrade == 'ثاني' ? '2' : '3');
              String sectionPrefix = selectedSection == 'أ'
                  ? 'A'
                  : (selectedSection == 'ب' ? 'B' : 'C');
              // Random 4-digit number
              String randomNum =
                  (1000 + DateTime.now().millisecondsSinceEpoch % 9000)
                      .toString();
              generatedId = '$deptPrefix$gradePrefix$sectionPrefix$randomNum';

              setState(() {});
            }

            return AlertDialog(
              title: const Text('إضافة مستخدم جديد'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Basic fields for all user types
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'الاسم',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),

                    // User Type Dropdown
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'نوع المستخدم',
                        border: OutlineInputBorder(),
                      ),
                      value: selectedUserType,
                      items: const [
                        DropdownMenuItem(value: 'admin', child: Text('مدير')),
                        DropdownMenuItem(value: 'teacher', child: Text('معلم')),
                        DropdownMenuItem(value: 'student', child: Text('طالب')),
                        DropdownMenuItem(
                          value: 'parent',
                          child: Text('ولي أمر'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedUserType = value;
                          // Reset other selections when user type changes
                          selectedDepartment = null;
                          selectedGrade = null;
                          selectedSection = null;
                          generatedId = '';
                        });
                      },
                    ),

                    // Conditional fields based on user type
                    if (selectedUserType == 'teacher') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedDepartment,
                        items: const [
                          DropdownMenuItem(
                            value: 'متوسط',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'ثانوي',
                            child: Text('ثانوي'),
                          ),
                          DropdownMenuItem(
                            value: 'كلاهما',
                            child: Text('كلاهما'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedDepartment = value;
                          });
                        },
                      ),
                    ],

                    if (selectedUserType == 'student') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedDepartment,
                        items: const [
                          DropdownMenuItem(
                            value: 'متوسط',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'ثانوي',
                            child: Text('ثانوي'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedDepartment = value;
                            generatedId = '';
                          });
                        },
                      ),

                      if (selectedDepartment != null) ...[
                        const SizedBox(height: AppConstants.paddingMedium),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الصف',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedGrade,
                          items: const [
                            DropdownMenuItem(value: 'أول', child: Text('أول')),
                            DropdownMenuItem(
                              value: 'ثاني',
                              child: Text('ثاني'),
                            ),
                            DropdownMenuItem(
                              value: 'ثالث',
                              child: Text('ثالث'),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedGrade = value;
                              generatedId = '';
                            });
                          },
                        ),

                        const SizedBox(height: AppConstants.paddingMedium),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الفصل',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedSection,
                          items: const [
                            DropdownMenuItem(value: 'أ', child: Text('أ')),
                            DropdownMenuItem(value: 'ب', child: Text('ب')),
                            DropdownMenuItem(value: 'ج', child: Text('ج')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedSection = value;
                              generatedId = '';
                            });
                          },
                        ),
                      ],

                      if (generatedId.isNotEmpty) ...[
                        const SizedBox(height: AppConstants.paddingMedium),
                        Container(
                          padding: const EdgeInsets.all(
                            AppConstants.paddingMedium,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(
                              AppConstants.radiusMedium,
                            ),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'رقم الطالب:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.textSecondaryColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              GestureDetector(
                                onTap: () {
                                  // Copy to clipboard when tapped
                                  Clipboard.setData(
                                    ClipboardData(text: generatedId),
                                  );
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'تم نسخ رقم الطالب إلى الحافظة',
                                      ),
                                      backgroundColor:
                                          AppConstants.successColor,
                                      duration: Duration(seconds: 1),
                                    ),
                                  );
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                    horizontal: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.blue.shade300,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        generatedId,
                                        style: const TextStyle(
                                          fontSize: AppConstants.fontSizeLarge,
                                          fontWeight: FontWeight.bold,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(
                                        Icons.copy,
                                        size: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    size: 14,
                                    color: AppConstants.textSecondaryColor,
                                  ),
                                  SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      'اضغط على الرقم لنسخه. سيتم استخدام هذا الرقم لربط الطالب بولي الأمر',
                                      style: TextStyle(
                                        fontSize: AppConstants.fontSizeSmall,
                                        color: AppConstants.textSecondaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],

                    if (selectedUserType == 'parent') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      TextField(
                        decoration: const InputDecoration(
                          labelText: 'رقم الطالب (ID)',
                          hintText: 'أدخل رقم الطالب المراد متابعته',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          // Handle student ID input for parent
                        },
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إضافة المستخدم بنجاح'),
                        backgroundColor: AppConstants.successColor,
                      ),
                    );
                  },
                  child: const Text('إضافة'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showReportsDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReportsScreen()),
    );
  }
}
