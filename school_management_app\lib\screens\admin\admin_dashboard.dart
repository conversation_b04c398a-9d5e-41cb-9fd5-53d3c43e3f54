import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';
import '../../widgets/modern_app_bar.dart';
import 'user_management_screen.dart';
import 'schedule_management_screen.dart';
import 'reports_screen.dart';
import 'admin_settings_screen.dart';
import '../auth/login_screen.dart';

class AdminDashboard extends StatefulWidget {
  const AdminDashboard({super.key});

  @override
  State<AdminDashboard> createState() => _AdminDashboardState();
}

class _AdminDashboardState extends State<AdminDashboard> {
  int _selectedIndex = 0;

  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();
    _screens = [
      AdminHomeScreen(
        onTabChange: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
      ),
      const UserManagementScreen(),
      const ScheduleManagementScreen(),
      const AdminSettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ModernAppBar(
        title: 'لوحة تحكم المدير',
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
          ),
        ],
      ),
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: 'إدارة المستخدمين',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.schedule),
            label: 'إدارة الجداول',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }

  Future<void> _logout(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();

    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
      );
    }
  }
}

class AdminHomeScreen extends StatelessWidget {
  final Function(int)? onTabChange;

  const AdminHomeScreen({super.key, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF667eea),
            const Color(0xFF764ba2),
            const Color(0xFFf093fb),
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section with Glass Effect
              Container(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingLarge,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.2),
                      Colors.white.withValues(alpha: 0.1),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    padding: const EdgeInsets.all(AppConstants.paddingLarge),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.orange.shade400,
                                Colors.pink.shade400,
                              ],
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.orange.withValues(alpha: 0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.admin_panel_settings,
                            size: 32,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingLarge),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'مرحباً بك، المدير',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black26,
                                      offset: Offset(0, 2),
                                      blurRadius: 4,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'لوحة التحكم الرئيسية',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'نشط الآن',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.white.withOpacity(0.9),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.notifications_active,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Statistics Section with Modern Cards
              Container(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingMedium,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 4,
                      height: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.orange, Colors.pink],
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'إحصائيات المدرسة',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            offset: Offset(0, 2),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Statistics Cards with Animation Effect
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.1,
                children: [
                  _buildModernStatCard(
                    'إجمالي الطلاب',
                    '150',
                    Icons.school,
                    [const Color(0xFF667eea), const Color(0xFF764ba2)],
                    '15%',
                    true,
                  ),
                  _buildModernStatCard(
                    'المعلمين',
                    '25',
                    Icons.person,
                    [const Color(0xFF11998e), const Color(0xFF38ef7d)],
                    '8%',
                    true,
                  ),
                  _buildModernStatCard(
                    'أولياء الأمور',
                    '120',
                    Icons.family_restroom,
                    [const Color(0xFFfc466b), const Color(0xFF3f5efb)],
                    '12%',
                    false,
                  ),
                  _buildModernStatCard(
                    'الصفوف',
                    '12',
                    Icons.class_,
                    [const Color(0xFFfdbb2d), const Color(0xFF22c1c3)],
                    '100%',
                    true,
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Quick Actions Section
              Container(
                margin: const EdgeInsets.only(
                  bottom: AppConstants.paddingMedium,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 4,
                      height: 30,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue, Colors.purple],
                        ),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'الإجراءات السريعة',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            offset: Offset(0, 2),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Quick Actions with Modern Design
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.2,
                children: [
                  _buildModernActionCard(
                    context,
                    'إضافة مستخدم',
                    'إضافة مستخدم جديد للنظام',
                    Icons.person_add_alt_1,
                    [const Color(0xFF667eea), const Color(0xFF764ba2)],
                    () => _showAddUserDialog(context),
                  ),
                  _buildModernActionCard(
                    context,
                    'الجداول الدراسية',
                    'رفع وإدارة الجداول',
                    Icons.calendar_today,
                    [const Color(0xFF11998e), const Color(0xFF38ef7d)],
                    () => _navigateToScheduleManagement(context),
                  ),
                  _buildModernActionCard(
                    context,
                    'إدارة المستخدمين',
                    'عرض وتعديل المستخدمين',
                    Icons.people_alt,
                    [const Color(0xFFfc466b), const Color(0xFF3f5efb)],
                    () => _navigateToUserManagement(context),
                  ),
                  _buildModernActionCard(
                    context,
                    'التقارير',
                    'عرض التقارير والإحصائيات',
                    Icons.analytics,
                    [const Color(0xFFfdbb2d), const Color(0xFF22c1c3)],
                    () => _showReportsDialog(context),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernStatCard(
    String title,
    String value,
    IconData icon,
    List<Color> gradientColors,
    String percentage,
    bool isPositive,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.25),
            Colors.white.withValues(alpha: 0.1),
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(colors: gradientColors),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: gradientColors[0].withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(icon, color: Colors.white, size: 24),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isPositive
                          ? Colors.green.withValues(alpha: 0.2)
                          : Colors.red.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isPositive ? Icons.trending_up : Icons.trending_down,
                          size: 12,
                          color: isPositive ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          percentage,
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: isPositive ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      offset: Offset(0, 2),
                      blurRadius: 4,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    List<Color> gradientColors,
    VoidCallback onTap,
  ) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.25),
            Colors.white.withValues(alpha: 0.1),
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: gradientColors),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: gradientColors[0].withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(icon, color: Colors.white, size: 24),
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        offset: Offset(0, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.white.withValues(alpha: 0.8),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Navigation methods for quick actions
  void _navigateToScheduleManagement(BuildContext context) {
    onTabChange?.call(2); // Schedule management tab
  }

  void _navigateToUserManagement(BuildContext context) {
    onTabChange?.call(1); // User management tab
  }

  void _showAddUserDialog(BuildContext context) {
    String? selectedUserType;
    String? selectedDepartment;
    String? selectedGrade;
    String? selectedSection;
    String generatedId = ''; // ID generated for new student

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Generate random ID for student
            if (selectedUserType == 'student' &&
                selectedDepartment != null &&
                selectedGrade != null &&
                selectedSection != null &&
                generatedId.isEmpty) {
              // Generate ID based on department, grade and section
              String deptPrefix = selectedDepartment == 'متوسط' ? 'M' : 'H';
              String gradePrefix = selectedGrade == 'أول'
                  ? '1'
                  : (selectedGrade == 'ثاني' ? '2' : '3');
              String sectionPrefix = selectedSection == 'أ'
                  ? 'A'
                  : (selectedSection == 'ب' ? 'B' : 'C');
              // Random 4-digit number
              String randomNum =
                  (1000 + DateTime.now().millisecondsSinceEpoch % 9000)
                      .toString();
              generatedId = '$deptPrefix$gradePrefix$sectionPrefix$randomNum';

              setState(() {});
            }

            return AlertDialog(
              title: const Text('إضافة مستخدم جديد'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Basic fields for all user types
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'الاسم',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'البريد الإلكتروني',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    TextField(
                      decoration: const InputDecoration(
                        labelText: 'كلمة المرور',
                        border: OutlineInputBorder(),
                      ),
                      obscureText: true,
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),

                    // User Type Dropdown
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'نوع المستخدم',
                        border: OutlineInputBorder(),
                      ),
                      value: selectedUserType,
                      items: const [
                        DropdownMenuItem(value: 'admin', child: Text('مدير')),
                        DropdownMenuItem(value: 'teacher', child: Text('معلم')),
                        DropdownMenuItem(value: 'student', child: Text('طالب')),
                        DropdownMenuItem(
                          value: 'parent',
                          child: Text('ولي أمر'),
                        ),
                      ],
                      onChanged: (value) {
                        setState(() {
                          selectedUserType = value;
                          // Reset other selections when user type changes
                          selectedDepartment = null;
                          selectedGrade = null;
                          selectedSection = null;
                          generatedId = '';
                        });
                      },
                    ),

                    // Conditional fields based on user type
                    if (selectedUserType == 'teacher') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedDepartment,
                        items: const [
                          DropdownMenuItem(
                            value: 'متوسط',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'ثانوي',
                            child: Text('ثانوي'),
                          ),
                          DropdownMenuItem(
                            value: 'كلاهما',
                            child: Text('كلاهما'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedDepartment = value;
                          });
                        },
                      ),
                    ],

                    if (selectedUserType == 'student') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'القسم',
                          border: OutlineInputBorder(),
                        ),
                        value: selectedDepartment,
                        items: const [
                          DropdownMenuItem(
                            value: 'متوسط',
                            child: Text('متوسط'),
                          ),
                          DropdownMenuItem(
                            value: 'ثانوي',
                            child: Text('ثانوي'),
                          ),
                        ],
                        onChanged: (value) {
                          setState(() {
                            selectedDepartment = value;
                            generatedId = '';
                          });
                        },
                      ),

                      if (selectedDepartment != null) ...[
                        const SizedBox(height: AppConstants.paddingMedium),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الصف',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedGrade,
                          items: const [
                            DropdownMenuItem(value: 'أول', child: Text('أول')),
                            DropdownMenuItem(
                              value: 'ثاني',
                              child: Text('ثاني'),
                            ),
                            DropdownMenuItem(
                              value: 'ثالث',
                              child: Text('ثالث'),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedGrade = value;
                              generatedId = '';
                            });
                          },
                        ),

                        const SizedBox(height: AppConstants.paddingMedium),
                        DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'الفصل',
                            border: OutlineInputBorder(),
                          ),
                          value: selectedSection,
                          items: const [
                            DropdownMenuItem(value: 'أ', child: Text('أ')),
                            DropdownMenuItem(value: 'ب', child: Text('ب')),
                            DropdownMenuItem(value: 'ج', child: Text('ج')),
                          ],
                          onChanged: (value) {
                            setState(() {
                              selectedSection = value;
                              generatedId = '';
                            });
                          },
                        ),
                      ],

                      if (generatedId.isNotEmpty) ...[
                        const SizedBox(height: AppConstants.paddingMedium),
                        Container(
                          padding: const EdgeInsets.all(
                            AppConstants.paddingMedium,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade50,
                            borderRadius: BorderRadius.circular(
                              AppConstants.radiusMedium,
                            ),
                            border: Border.all(color: Colors.blue.shade200),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'رقم الطالب:',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: AppConstants.textSecondaryColor,
                                ),
                              ),
                              const SizedBox(height: 4),
                              GestureDetector(
                                onTap: () {
                                  // Copy to clipboard when tapped
                                  Clipboard.setData(
                                    ClipboardData(text: generatedId),
                                  );
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'تم نسخ رقم الطالب إلى الحافظة',
                                      ),
                                      backgroundColor:
                                          AppConstants.successColor,
                                      duration: Duration(seconds: 1),
                                    ),
                                  );
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 8,
                                    horizontal: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.blue.shade300,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        generatedId,
                                        style: const TextStyle(
                                          fontSize: AppConstants.fontSizeLarge,
                                          fontWeight: FontWeight.bold,
                                          color: AppConstants.primaryColor,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      const Icon(
                                        Icons.copy,
                                        size: 18,
                                        color: AppConstants.primaryColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              const Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    size: 14,
                                    color: AppConstants.textSecondaryColor,
                                  ),
                                  SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      'اضغط على الرقم لنسخه. سيتم استخدام هذا الرقم لربط الطالب بولي الأمر',
                                      style: TextStyle(
                                        fontSize: AppConstants.fontSizeSmall,
                                        color: AppConstants.textSecondaryColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],

                    if (selectedUserType == 'parent') ...[
                      const SizedBox(height: AppConstants.paddingMedium),
                      TextField(
                        decoration: const InputDecoration(
                          labelText: 'رقم الطالب (ID)',
                          hintText: 'أدخل رقم الطالب المراد متابعته',
                          border: OutlineInputBorder(),
                        ),
                        onChanged: (value) {
                          // Handle student ID input for parent
                        },
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إضافة المستخدم بنجاح'),
                        backgroundColor: AppConstants.successColor,
                      ),
                    );
                  },
                  child: const Text('إضافة'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showReportsDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReportsScreen()),
    );
  }
}
