import 'package:flutter/material.dart';
import '../utils/constants.dart';

class ModernAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final bool showGradient;
  final double elevation;

  const ModernAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.showBackButton = true,
    this.onBackPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.showGradient = true,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: showGradient
          ? BoxDecoration(
              gradient: AppConstants.primaryGradient,
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            )
          : BoxDecoration(
              color: backgroundColor ?? AppConstants.primaryColor,
              boxShadow: elevation > 0
                  ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: elevation * 2,
                        offset: Offset(0, elevation),
                      ),
                    ]
                  : null,
            ),
      child: AppBar(
        title: Text(
          title,
          style: TextStyle(
            fontWeight: AppConstants.fontWeightSemiBold,
            fontSize: AppConstants.fontSizeXLarge,
            color: foregroundColor ?? AppConstants.textOnPrimaryColor,
            fontFamily: 'Cairo',
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: actions,
        leading: leading ??
            (showBackButton && Navigator.of(context).canPop()
                ? IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: foregroundColor ?? AppConstants.textOnPrimaryColor,
                    ),
                    onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                  )
                : null),
        centerTitle: true,
        iconTheme: IconThemeData(
          color: foregroundColor ?? AppConstants.textOnPrimaryColor,
          size: AppConstants.iconSizeMedium,
        ),
        actionsIconTheme: IconThemeData(
          color: foregroundColor ?? AppConstants.textOnPrimaryColor,
          size: AppConstants.iconSizeMedium,
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class ModernSliverAppBar extends StatelessWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool pinned;
  final bool floating;
  final double expandedHeight;
  final Widget? flexibleSpace;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ModernSliverAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.pinned = true,
    this.floating = false,
    this.expandedHeight = 200.0,
    this.flexibleSpace,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      title: Text(
        title,
        style: TextStyle(
          fontWeight: AppConstants.fontWeightSemiBold,
          fontSize: AppConstants.fontSizeXLarge,
          color: foregroundColor ?? AppConstants.textOnPrimaryColor,
          fontFamily: 'Cairo',
        ),
      ),
      actions: actions,
      leading: leading,
      pinned: pinned,
      floating: floating,
      expandedHeight: expandedHeight,
      flexibleSpace: flexibleSpace ??
          FlexibleSpaceBar(
            background: Container(
              decoration: const BoxDecoration(
                gradient: AppConstants.primaryGradient,
              ),
            ),
          ),
      backgroundColor: backgroundColor ?? Colors.transparent,
      elevation: 0,
      centerTitle: true,
      iconTheme: IconThemeData(
        color: foregroundColor ?? AppConstants.textOnPrimaryColor,
        size: AppConstants.iconSizeMedium,
      ),
      actionsIconTheme: IconThemeData(
        color: foregroundColor ?? AppConstants.textOnPrimaryColor,
        size: AppConstants.iconSizeMedium,
      ),
    );
  }
}
