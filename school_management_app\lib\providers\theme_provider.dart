import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/constants.dart';

class ThemeProvider extends ChangeNotifier {
  static const String _themeKey = 'theme_mode';
  static const String _colorSchemeKey = 'color_scheme';
  
  ThemeMode _themeMode = ThemeMode.system;
  ColorSchemeType _colorScheme = ColorSchemeType.blue;
  
  ThemeMode get themeMode => _themeMode;
  ColorSchemeType get colorScheme => _colorScheme;
  
  bool get isDarkMode => _themeMode == ThemeMode.dark;
  bool get isLightMode => _themeMode == ThemeMode.light;
  bool get isSystemMode => _themeMode == ThemeMode.system;

  ThemeProvider() {
    _loadThemeFromPrefs();
  }

  Future<void> _loadThemeFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeModeIndex = prefs.getInt(_themeKey) ?? ThemeMode.system.index;
      final colorSchemeIndex = prefs.getInt(_colorSchemeKey) ?? ColorSchemeType.blue.index;
      
      _themeMode = ThemeMode.values[themeModeIndex];
      _colorScheme = ColorSchemeType.values[colorSchemeIndex];
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme: $e');
    }
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    if (_themeMode == mode) return;
    
    _themeMode = mode;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_themeKey, mode.index);
    } catch (e) {
      debugPrint('Error saving theme mode: $e');
    }
  }

  Future<void> setColorScheme(ColorSchemeType scheme) async {
    if (_colorScheme == scheme) return;
    
    _colorScheme = scheme;
    notifyListeners();
    
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_colorSchemeKey, scheme.index);
    } catch (e) {
      debugPrint('Error saving color scheme: $e');
    }
  }

  void toggleTheme() {
    switch (_themeMode) {
      case ThemeMode.light:
        setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        setThemeMode(ThemeMode.system);
        break;
      case ThemeMode.system:
        setThemeMode(ThemeMode.light);
        break;
    }
  }

  ThemeData get lightTheme => _buildLightTheme();
  ThemeData get darkTheme => _buildDarkTheme();

  ThemeData _buildLightTheme() {
    final colors = _getColorScheme();
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: ColorScheme.fromSeed(
        seedColor: colors.primary,
        brightness: Brightness.light,
        primary: colors.primary,
        secondary: colors.secondary,
        surface: colors.surface,
        error: colors.error,
      ),
      fontFamily: 'Cairo',
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: colors.onPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: AppConstants.fontWeightSemiBold,
          color: colors.onPrimary,
          fontFamily: 'Cairo',
        ),
        iconTheme: IconThemeData(
          color: colors.onPrimary,
          size: AppConstants.iconSizeMedium,
        ),
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: AppConstants.elevationLow,
        shadowColor: colors.primary.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
        color: colors.surface,
        margin: const EdgeInsets.all(AppConstants.paddingSmall),
      ),
      
      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colors.primary,
          foregroundColor: colors.onPrimary,
          elevation: AppConstants.elevationMedium,
          shadowColor: colors.primary.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingXLarge,
            vertical: AppConstants.paddingMedium,
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: AppConstants.fontWeightSemiBold,
            fontFamily: 'Cairo',
          ),
          minimumSize: const Size(0, AppConstants.buttonHeightLarge),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: BorderSide(
            color: colors.outline,
            width: 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: BorderSide(
            color: colors.outline,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: BorderSide(
            color: colors.primary,
            width: 2.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          borderSide: BorderSide(
            color: colors.error,
            width: 1.0,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingLarge,
          vertical: AppConstants.paddingMedium,
        ),
        labelStyle: TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          color: colors.onSurfaceVariant,
          fontFamily: 'Cairo',
        ),
        hintStyle: TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          color: colors.onSurfaceVariant.withValues(alpha: 0.6),
          fontFamily: 'Cairo',
        ),
      ),
      
      // Text Themes
      textTheme: TextTheme(
        displayLarge: TextStyle(
          fontSize: AppConstants.fontSizeDisplay,
          fontWeight: AppConstants.fontWeightBold,
          color: colors.onSurface,
          fontFamily: 'Cairo',
        ),
        headlineLarge: TextStyle(
          fontSize: AppConstants.fontSizeHeading,
          fontWeight: AppConstants.fontWeightBold,
          color: colors.onSurface,
          fontFamily: 'Cairo',
        ),
        titleLarge: TextStyle(
          fontSize: AppConstants.fontSizeTitle,
          fontWeight: AppConstants.fontWeightSemiBold,
          color: colors.onSurface,
          fontFamily: 'Cairo',
        ),
        bodyLarge: TextStyle(
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: AppConstants.fontWeightRegular,
          color: colors.onSurface,
          fontFamily: 'Cairo',
        ),
        bodyMedium: TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: AppConstants.fontWeightRegular,
          color: colors.onSurface,
          fontFamily: 'Cairo',
        ),
        labelLarge: TextStyle(
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: AppConstants.fontWeightMedium,
          color: colors.onSurfaceVariant,
          fontFamily: 'Cairo',
        ),
      ),
      
      // Icon Theme
      iconTheme: IconThemeData(
        color: colors.onSurfaceVariant,
        size: AppConstants.iconSizeMedium,
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: colors.outline,
        thickness: 1.0,
        space: 1.0,
      ),
    );
  }

  ThemeData _buildDarkTheme() {
    final colors = _getColorScheme();
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: colors.primary,
        brightness: Brightness.dark,
        primary: colors.primary,
        secondary: colors.secondary,
        surface: const Color(0xFF121212),
        error: colors.error,
      ),
      fontFamily: 'Cairo',
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: const TextStyle(
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: AppConstants.fontWeightSemiBold,
          color: Colors.white,
          fontFamily: 'Cairo',
        ),
        iconTheme: const IconThemeData(
          color: Colors.white,
          size: AppConstants.iconSizeMedium,
        ),
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: AppConstants.elevationLow,
        shadowColor: Colors.black.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        ),
        color: const Color(0xFF1E1E1E),
        margin: const EdgeInsets.all(AppConstants.paddingSmall),
      ),
      
      // Similar themes for dark mode...
      // (يمكن إضافة المزيد من التخصيصات هنا)
    );
  }

  AppColorScheme _getColorScheme() {
    switch (_colorScheme) {
      case ColorSchemeType.blue:
        return AppColorScheme.blue();
      case ColorSchemeType.green:
        return AppColorScheme.green();
      case ColorSchemeType.purple:
        return AppColorScheme.purple();
      case ColorSchemeType.orange:
        return AppColorScheme.orange();
      case ColorSchemeType.red:
        return AppColorScheme.red();
    }
  }
}

enum ColorSchemeType {
  blue,
  green,
  purple,
  orange,
  red,
}

class AppColorScheme {
  final Color primary;
  final Color secondary;
  final Color surface;
  final Color error;
  final Color onPrimary;
  final Color onSurface;
  final Color onSurfaceVariant;
  final Color outline;

  const AppColorScheme({
    required this.primary,
    required this.secondary,
    required this.surface,
    required this.error,
    required this.onPrimary,
    required this.onSurface,
    required this.onSurfaceVariant,
    required this.outline,
  });

  factory AppColorScheme.blue() => const AppColorScheme(
    primary: Color(0xFF1E3A8A),
    secondary: Color(0xFFF59E0B),
    surface: Color(0xFFFFFFFF),
    error: Color(0xFFEF4444),
    onPrimary: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1F2937),
    onSurfaceVariant: Color(0xFF6B7280),
    outline: Color(0xFFE5E7EB),
  );

  factory AppColorScheme.green() => const AppColorScheme(
    primary: Color(0xFF059669),
    secondary: Color(0xFFF59E0B),
    surface: Color(0xFFFFFFFF),
    error: Color(0xFFEF4444),
    onPrimary: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1F2937),
    onSurfaceVariant: Color(0xFF6B7280),
    outline: Color(0xFFE5E7EB),
  );

  factory AppColorScheme.purple() => const AppColorScheme(
    primary: Color(0xFF7C3AED),
    secondary: Color(0xFFF59E0B),
    surface: Color(0xFFFFFFFF),
    error: Color(0xFFEF4444),
    onPrimary: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1F2937),
    onSurfaceVariant: Color(0xFF6B7280),
    outline: Color(0xFFE5E7EB),
  );

  factory AppColorScheme.orange() => const AppColorScheme(
    primary: Color(0xFFEA580C),
    secondary: Color(0xFF3B82F6),
    surface: Color(0xFFFFFFFF),
    error: Color(0xFFEF4444),
    onPrimary: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1F2937),
    onSurfaceVariant: Color(0xFF6B7280),
    outline: Color(0xFFE5E7EB),
  );

  factory AppColorScheme.red() => const AppColorScheme(
    primary: Color(0xFFDC2626),
    secondary: Color(0xFF3B82F6),
    surface: Color(0xFFFFFFFF),
    error: Color(0xFFEF4444),
    onPrimary: Color(0xFFFFFFFF),
    onSurface: Color(0xFF1F2937),
    onSurfaceVariant: Color(0xFF6B7280),
    outline: Color(0xFFE5E7EB),
  );
}
